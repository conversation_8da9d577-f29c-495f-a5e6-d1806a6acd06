from apps.connectors.integrations import IntegrationActionType, TemplateVersion
from apps.connectors.integrations.vendors.palo_alto.palo_alto_prisma_cloud.v1 import (
    PaloAltoPrismaCloudV1Settings,
)
from apps.demo.integrations.template import DemoConnectionTemplate, DemoTemplateConfig

from .integration import DemoPaloAltoPrismaCloudV1Integration


class DemoPaloAltoPrismaCloudV1TemplateVersion(TemplateVersion):
    integration = DemoPaloAltoPrismaCloudV1Integration
    id = "v1"
    name = "v1"
    supported_actions = []
    settings_model = PaloAltoPrismaCloudV1Settings
    config_model = DemoTemplateConfig
    connection_model = DemoConnectionTemplate
