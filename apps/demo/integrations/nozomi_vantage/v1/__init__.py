
from apps.connectors.integrations import IntegrationActionType, TemplateVersion
from apps.connectors.integrations.nozomi_vantage.v1 import NozomiVantageV1Settings
from apps.demo.integrations.template import DemoTemplateConfig

from .integration import DemoNozomiVantageV1Integration

class DemoNozomiVantageV1TemplateVersion(TemplateVersion):
    integration = DemoNozomiVantageV1Integration
    id = "v1"
    name = "v1"
    supported_actions = []
    settings_model = NozomiVantageV1Settings
    config_model = DemoTemplateConfig
