from typing import Literal

from microsoft_client.defender_atp import DefenderATPClient

from apps.connectors.integrations.vendors.microsoft.ms_xdr.v1.api import MsXdrV1Api


class MsMdeV1Api(MsXdrV1Api):
    def __init__(self, tenant_id=None, client_id=None, client_secret=None, **kwargs):
        super().__init__(
            tenant_id=tenant_id,
            client_id=client_id,
            client_secret=client_secret,
            **kwargs,
        )

        self.endpoint_client = DefenderATPClient(
            None,
            None,
            None,
            tenant_id=tenant_id,
            client_id=client_id,
            client_secret=client_secret,
        )

    def get_latest_machine_isolation_action(self, host_identifier):
        """
        Get the latest isolation action for a specific host using its identifier.
        :param host_identifier: The identifier of the host.
        :return: Response from the API.
        """

        params = {
            "$filter": f"machineID eq '{host_identifier}' and type in ('Isolate','Unisolate')",
            "$top": "1",
        }
        actions = self.endpoint_client.machines.list_actions(params=params)
        if actions["value"]:
            return actions["value"][0]
        return None

    def get_machine_action(self, action_id):
        """
        Get actions for a specific host using its identifier.
        :param action_id: The identifier of the action.
        :return: Response from the API.
        """
        return self.endpoint_client.machines.get_action(action_id=action_id)

    def isolate_host(
        self,
        host_identifier,
        console_comment,
        isolation_type: Literal["Full", "Selective"],
    ):
        """
        Isolate a host using its identifier.
        :param host_identifier: The identifier of the host to isolate.
        :return: Response from the API.
        """
        return self.endpoint_client.machines.isolate(
            machine_id=host_identifier,
            comment=console_comment,
            isolation_type=isolation_type,
        )

    def unisolate_host(self, host_identifier, console_comment):
        """
        Unisolate a host using its identifier.
        :param host_identifier: The identifier of the host to unisolate.
        :return: Response from the API.
        """
        return self.endpoint_client.machines.release(
            machine_id=host_identifier,
            comment=console_comment,
        )
