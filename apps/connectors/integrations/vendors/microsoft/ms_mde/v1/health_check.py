from apps.connectors.integrations.health_check import (
    IntegrationHealthCheckRequirementStatus,
    SimplePermissionsCheck,
)


class IsolateMachine(SimplePermissionsCheck):
    name = "Isolate and Unisolate Machines"
    description = (
        "Isolate and Unisolate Machines in Microsoft Defender for Endpoint API"
    )
    value = "Isolate and Unisolate Machines"
    required = IntegrationHealthCheckRequirementStatus.REQUIRED
    allowed_values = [
        "Machine.Isolate",
    ]


class ListMachineActions(SimplePermissionsCheck):
    name = "List Machine Actions"
    description = "List Machine Actions in Microsoft Defender for Endpoint API"
    value = "List Machine Actions"
    required = IntegrationHealthCheckRequirementStatus.REQUIRED
    allowed_values = [
        "Machine.Read.All",
        "Machine.ReadWrite.All",
    ]
