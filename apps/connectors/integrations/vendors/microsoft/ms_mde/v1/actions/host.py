from enum import StrEnum
from typing import Literal

from apps.connectors.integrations.actions.host import (
    <PERSON><PERSON><PERSON><PERSON>rg<PERSON>,
    HostIsolationResult,
    IsolateHostAction,
    UnisolateHostAction,
)
from apps.connectors.integrations.schemas import (
    Message,
)
from apps.connectors.integrations.schemas.action_polling_context import (
    IntegrationActionPollingContext,
)
from apps.connectors.integrations.schemas.tap_result import ErrorDetail
from apps.connectors.integrations.vendors.microsoft.ms_mde.v1.actions import (
    MsMdeV1InternalActions,
)
from apps.connectors.integrations.vendors.microsoft.ms_mde.v1.api import MsMdeV1Api
from apps.connectors.integrations.vendors.microsoft.ms_mde.v1.health_check import (
    IsolateMachine,
    ListMachineActions,
)


class MachineIsolationType(StrEnum):
    FULL = "Full"
    SELECTIVE = "Selective"


# For reference, this is an example of an MDE MachineAction:
# {
#         "id": "5382f7ea-7557-4ab7-9782-d50480024a4e",
#         "type": "Isolate",
#         "scope": "Selective",
#         "requestor": "<EMAIL>",
#         "requestorComment": "test for docs",
#         "status": "Succeeded",
#         "machineId": "7b1f4967d9728e5aa3c06a9e617a22a4a5a17378",
#         "computerDnsName": "desktop-test",
#         "creationDateTimeUtc": "2019-01-02T14:39:38.2262283Z",
#         "lastUpdateDateTimeUtc": "2019-01-02T14:40:44.6596267Z",
#         "relatedFileInfo": null
# }
#
# Possible values for status are Pending, InProgress, Succeeded, Failed, TimeOut, and Cancelled.


def result_from_action(
    host_id: str,
    isolation_type: MachineIsolationType,
    machine_action: dict,
) -> HostIsolationResult | IntegrationActionPollingContext:
    if machine_action["status"] == "Succeeded":
        return HostIsolationResult(
            result=Message(
                message=f"Machine {host_id} action {isolation_type} completed successfully."
            )
        )
    elif machine_action["status"] == "Pending":
        return IntegrationActionPollingContext(
            context={
                "host_id": host_id,
                "isolation_type": isolation_type,
                "machine_action": machine_action,
            }
        )
    else:
        return HostIsolationResult(
            error=ErrorDetail(
                message=f"Machine {host_id} action {isolation_type} failed with status {machine_action['status']}"
            )
        )


def update_machine_isolation(
    api: MsMdeV1Api,
    action: Literal["Isolate", "Unisolate"],
    args: HostIsolationArgs,
    isolation_type: MachineIsolationType,
) -> HostIsolationResult | IntegrationActionPollingContext:
    host_id = args.host.value

    latest_action = api.get_latest_machine_isolation_action(host_id)
    if latest_action and latest_action["type"] == action:
        return HostIsolationResult(
            error=ErrorDetail(
                message=f"Machine {host_id} is already {action.lower()}d."
            )
        )

    if action == "Isolate":
        machine_action = api.isolate_host(host_id, args.console_comment, isolation_type)
    else:
        machine_action = api.unisolate_host(host_id, args.console_comment)

    return result_from_action(host_id, isolation_type, machine_action)


def poll_for_action_status(
    api: MsMdeV1Api,
    polling_context: IntegrationActionPollingContext,
) -> IntegrationActionPollingContext:
    host_id = polling_context.context.get("host_id")
    isolation_type = polling_context.context.get("isolation_type")
    machine_action = polling_context.context.get("machine_action")

    action = api.get_machine_action(machine_action["id"])
    return result_from_action(host_id, isolation_type, action)


class MsMdeV1IsolateHost(IsolateHostAction):
    def execute(
        self, args: HostIsolationArgs, **kwargs
    ) -> HostIsolationResult | IntegrationActionPollingContext:
        api: MsMdeV1Api = self.integration.get_api()

        return update_machine_isolation(
            api,
            "Isolate",
            args,
            MachineIsolationType.FULL,
        )

    def poll(
        self, poll_context: IntegrationActionPollingContext
    ) -> IntegrationActionPollingContext:
        api: MsMdeV1Api = self.integration.get_api()
        return poll_for_action_status(api, poll_context)

    def get_permission_checks(self, *args, **kwargs):
        return [IsolateMachine, ListMachineActions]


class MsMdeV1SelectiveIsolateHost(IsolateHostAction):
    name = "Selective Isolate Host"
    action_type = MsMdeV1InternalActions.SELECTIVE_HOST_ISOLATE

    def execute(
        self, args: HostIsolationArgs, **kwargs
    ) -> HostIsolationResult | IntegrationActionPollingContext:
        api: MsMdeV1Api = self.integration.get_api()

        return update_machine_isolation(
            api,
            "Isolate",
            args,
            MachineIsolationType.SELECTIVE,
        )

    def poll(
        self, poll_context: IntegrationActionPollingContext
    ) -> IntegrationActionPollingContext:
        api: MsMdeV1Api = self.integration.get_api()
        return poll_for_action_status(api, poll_context)

    def get_permission_checks(self, *args, **kwargs):
        return [IsolateMachine, ListMachineActions]


class MsMdeV1UnisolateHost(UnisolateHostAction):
    def execute(
        self, args: HostIsolationArgs, **kwargs
    ) -> HostIsolationResult | IntegrationActionPollingContext:
        api: MsMdeV1Api = self.integration.get_api()

        return update_machine_isolation(
            api,
            "Unisolate",
            args,
            MachineIsolationType.FULL,
        )

    def poll(
        self, poll_context: IntegrationActionPollingContext
    ) -> IntegrationActionPollingContext:
        api: MsMdeV1Api = self.integration.get_api()
        return poll_for_action_status(api, poll_context)

    def get_permission_checks(self, *args, **kwargs):
        return [IsolateMachine, ListMachineActions]
