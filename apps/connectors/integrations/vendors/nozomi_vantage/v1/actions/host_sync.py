
from typing import Generator

from apps.connectors.integrations.actions import normalize, normalize_last_seen, to_list
from apps.connectors.integrations.actions.host_sync import (
    Host,
    HostSync,
    HostSyncArgs,
    AssetCriticality,
)

def normalize_host(host_data: dict):
    # Logics goes here
    return Host(
        source_id="",
        group_names=[],
        hostname="",
        fqdns=[],
        ip_addresses=[],
        mac_addresses=[],
        os="",  # OsAttributes
        owners=[],  # List[OwnerAttributes]
        aad_id=None,
        criticality=AssetCriticality.UNKNOWN,
        last_seen=normalize_last_seen(host_data.get("KEY_TO_LAST_SEEN")), # only works for iso date string or list of iso date strings
        source_data=host_data,
    )

class NozomiVantageV1HostSync(HostSync):
    @normalize(normalize_host)
    def execute(self, args: HostSyncArgs, **kwargs) -> Generator[Host, None, None]:
        pass

    def get_permission_checks(self):
        pass
