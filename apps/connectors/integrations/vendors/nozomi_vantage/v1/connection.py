
from pydantic import Field, HttpUrl

from apps.connectors.integrations import ConnectionTemplate, TemplateVersionConfig
from apps.connectors.integrations.types import EncryptedStr


class NozomiVantageV1Config(TemplateVersionConfig):
    url: HttpUrl = Field(
        title="Vantage URL",
        description="The base URL for the Nozomi Vantage API endpoint",
    )
    api_key_name: str = Field(
        title="API Key Name",
        description="The API key name for authentication with Nozomi Vantage",
        max_length=1024,
    )
    api_key_token: EncryptedStr = Field(
        title="API Key Token",
        description="The API key token for authentication with Nozomi Vantage",
        max_length=1024,
    )


class NozomiVantageV1Connection(ConnectionTemplate):
    id = "nozomi_vantage"
    name = "Nozomi Vantage"
    config_model = NozomiVantageV1Config
