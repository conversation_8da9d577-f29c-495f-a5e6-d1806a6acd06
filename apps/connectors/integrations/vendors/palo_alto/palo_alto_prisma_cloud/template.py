from apps.connectors.integrations import Template
from apps.connectors.integrations.vendors.vendor import Vendors

from .v1 import PaloAltoPrismaCloudV1TemplateVersion


class PaloAltoPrismaCloudTemplate(Template):
    id = "palo_alto_prisma_cloud"
    name = "Palo Alto Prisma Cloud"
    category = Template.Category.CLOUD_SECURITY
    versions = {
        PaloAltoPrismaCloudV1TemplateVersion.id: PaloAltoPrismaCloudV1TemplateVersion(),
    }
    vendor = Vendors.PALO_ALTO
