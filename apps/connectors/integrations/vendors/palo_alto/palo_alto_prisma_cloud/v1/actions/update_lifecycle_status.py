from apps.connectors.integrations.actions.update_lifecycle_status import (
    CorrIncidentStatus,
    UpdateLifecycleStatus,
    UpdateLifecycleStatusArgs,
    UpdateLifecycleStatusResult,
)
from apps.connectors.integrations.vendors.palo_alto.palo_alto_prisma_cloud.v1.api import (
    PaloAltoPrismaCloudV1Api,
)


def map_corr_incident_status(status: CorrIncidentStatus) -> str:
    return {
        CorrIncidentStatus.NEW: "Archived False",
        CorrIncidentStatus.ASSIGNED: "Archived False",
        CorrIncidentStatus.REVIEWING: "Archived False",
        CorrIncidentStatus.MITIGATED: "Archived True",
        CorrIncidentStatus.CLOSED: "Archived True",
    }[status]


class PaloAltoPrismaCloudV1UpdateLifecycleStatus(UpdateLifecycleStatus):
    def execute(
        self, args: UpdateLifecycleStatusArgs, **kwargs
    ) -> UpdateLifecycleStatusResult:
        api: PaloAltoPrismaCloudV1Api = self.integration.get_api()

        update_data = {
            "status": map_corr_incident_status(args.status),
        }
        alert_id = args.vendor_sync_id
        if update_data["status"] == "Archived True":
            api.dismiss_alert(alert_id)
        else:
            api.reopen_alert(alert_id)

        return UpdateLifecycleStatusResult()

    def get_permission_checks(self, *args, **kwargs):
        return []  # pragma: no cover
