from datetime import datetime, timezone
from typing import Generator

from dateutil import parser

from apps.connectors.integrations.actions.event_sync import (
    Event,
    EventSync,
    EventSyncArgs,
    VendorRefExtended,
)
from apps.connectors.integrations.actions.utils import normalize, to_list
from apps.connectors.integrations.schemas import ocsf
from apps.connectors.integrations.schemas.ocsf import (
    Account,
    Cloud,
    DetectionActivity,
    DetectionFinding,
    DetectionStatus,
    EvidenceArtifacts,
    FindingInformation,
    Metadata,
    NetworkEndpoint,
    ResourceDetails,
    User,
)
from apps.connectors.integrations.vendors.palo_alto.palo_alto_prisma_cloud.v1.api import (
    PaloAltoPrismaCloudV1Api,
    paginate,
)
from apps.connectors.integrations.vendors.palo_alto.palo_alto_prisma_cloud.v1.bookmarks import (
    PaloAltoPrismaCloudEventSyncBookmark,
)


def parse_datetime(dt_str: str) -> datetime:
    return parser.parse(dt_str)


def get_iso_date(dt_str: str) -> datetime:
    return parser.parse(dt_str).isoformat()


def format_datetime(dt: datetime) -> str:
    # YYYY-MM-DDTHH:MM:SSZ
    return dt.strftime("%Y-%m-%dT%H:%M:%SZ")


def get_severity(crlevel) -> ocsf.Severity:
    if crlevel == "critical":
        return ocsf.Severity.CRITICAL
    elif crlevel == "high":
        return ocsf.Severity.HIGH
    elif crlevel == "elevated":
        return ocsf.Severity.OTHER
    elif crlevel == "medium":
        return ocsf.Severity.MEDIUM
    elif crlevel == "low":
        return ocsf.Severity.LOW
    else:
        return ocsf.Severity.UNKNOWN


def get_status(status: str) -> str:
    if status == "open":
        return DetectionStatus.NEW
    elif status == "dismissed":
        return DetectionStatus.SUPPRESSED
    elif status == "snoozed":
        return DetectionStatus.ARCHIVED
    elif status == "resolved":
        return DetectionStatus.RESOLVED
    elif status == "pending_resolution":
        return DetectionStatus.IN_PROGRESS


def get_evidences(event: dict) -> list[EvidenceArtifacts]:
    evidences = []
    connection_details = event.get("connectionDetails", [])
    for connection in connection_details:
        evidences.append(
            EvidenceArtifacts(
                src_endpoint=NetworkEndpoint(
                    ip=connection.get("srcIp"),
                    isp=connection.get("srcIsp"),
                ),
                dst_endpoint=NetworkEndpoint(
                    ip=connection.get("destIp"),
                    isp=connection.get("destIsp"),
                ),
                name=connection.get("classification"),
                verdict=connection.get("accepted"),
                uid=str(connection.get("id")),
                data={
                    "feedSource": connection.get("feedSource"),
                    "bytes_in": connection.get("inboundTrafficVolume"),
                    "bytes_out": connection.get("outboundTrafficVolume"),
                    "packets": connection.get("packets"),
                    "markedThreatTs": connection.get("markedThreatTs"),
                    "timestamp": connection.get("timestamp"),
                    "trafficOverTime": connection.get("trafficOverTime"),
                    "bytes": connection.get("trafficVolume"),
                },
            )
        )

    return evidences


def convert_to_ocsf(event: dict) -> DetectionFinding:
    return DetectionFinding(
        activity=DetectionActivity.OTHER,
        comment=event.get("dismissalNote"),
        metadata=Metadata(
            correlation_uid=event.get("id"),
            uid=event.get("id"),
            event_code=None,
            profiles=[],
        ),
        message=event.get("reason"),
        severity=get_severity(event.get("policy", {}).get("severity")),
        impact=event.get("riskDetail", {}).get("rating"),
        confidence_score=int(
            event.get("riskDetail", {}).get("riskScore", {}).get("score", 0)
        ),
        impact_score=int(event.get("riskDetail", {}).get("score", 0)),
        status=get_status(event.get("status")),
        finding_info=FindingInformation(
            types=event.get("policy", {}).get("findingTypes", []),
            desc=event.get("policy", {}).get("description"),
            policy=event.get("policy", {}).get("name"),
            uid=event.get("policy", {}).get("policyId"),
            name=event.get("policy", {}).get("name"),
            is_applied=event.get("policy", {}).get("enabled"),
            first_seen_time_dt=get_iso_date(event.get("firstSeen")),
            last_seen_time_dt=get_iso_date(event.get("lastSeen")),
            modified_time_dt=get_iso_date(event.get("lastUpdated")),
            data=event.get("riskDetail", {}).get("policyScores", {}),
        ),
        resources=[
            ResourceDetails(
                created_time=event.get("alertAttribution").get("resourceCreatedOn"),
                modified_time=event.get("alertAttribution").get("resourceModifiedOn"),
                uid_alt=event.get("resource", {}).get("rrn"),
                uid=event.get("resource", {}).get("id"),
                name=event.get("resource", {}).get("name"),
                type=event.get("resource", {}).get("resourceType"),
                data=event.get("resource", {}).get("additionalInfo", {})
                or event.get("resource", {}).get("data", {}),
                tags=to_list(event.get("resource", {}).get("resourceTags", [])),
                owner=User(
                    name=event.get("resource", {}).get("cloudAccountOwners", [])[0]
                ),
            )
        ],
        time_dt=get_iso_date(event.get("alertTime")),
        evidences=get_evidences(event),
        cloud=Cloud(
            provider=event.get("policy", {}).get("cloudType"),
            account=Account(
                uid=event.get("resource", {}).get("accountId"),
                name=event.get("resource", {}).get("account"),
            ),
            region=event.get("resource", {}).get("region"),
        ),
    )


def normalize_event(event: dict) -> Event:
    return Event(
        event_timestamp=get_iso_date(event.get("alertTime")),
        raw_event=event,
        ocsf=convert_to_ocsf(event),
        vendor_item_ref=VendorRefExtended(
            id=event.get("id"),
            title="Palo Alto Prisma Cloud Alert",
        ),
        vendor_group_ref=None,
    )


class PaloAltoPrismaCloudV1EventSync(EventSync):
    @normalize(normalize_event)
    def execute(
        self,
        args: EventSyncArgs,
        bookmark: PaloAltoPrismaCloudEventSyncBookmark = None,
        **kwargs,
    ) -> Generator[Event, None, None]:
        api: PaloAltoPrismaCloudV1Api = self.integration.get_api()
        query_receive_time = datetime.now(timezone.utc) - parse_datetime(
            bookmark.query_receive_time
        )
        latest_event_update_datetime = parse_datetime(bookmark.query_receive_time)
        alerts = paginate(
            api.list_alerts,
            time_amount=int(query_receive_time.total_seconds() // 60),
            time_unit="minute",
            detailed=True,
        )
        for alert in alerts:
            alert_detail = api.get_alert_detail(alert.get("id"))
            yield alert_detail
            if (
                parse_datetime(alert_detail.get("alertTime"))
                > latest_event_update_datetime
            ):
                latest_event_update_datetime = parse_datetime(
                    alert_detail.get("alertTime")
                )
        bookmark.query_receive_time = format_datetime(latest_event_update_datetime)

    def get_permission_checks(self):
        return []  # pragma: no cover
