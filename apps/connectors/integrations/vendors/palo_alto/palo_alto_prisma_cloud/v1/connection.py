from pydantic import Field, HttpUrl

from apps.connectors.integrations import ConnectionTemplate, TemplateVersionConfig
from apps.connectors.integrations.types import EncryptedStr


class PaloAltoPrismaCloudV1Config(TemplateVersionConfig):
    # https://pan.dev/prisma-cloud/api/cspm/app-login/
    base_url: HttpUrl = Field(
        title="Prisma Cloud API URL",
        description="The URL of the Prisma Cloud API.",
    )
    username: str = Field(
        title="Access key ID or Username",
        description="The access key ID or username to use for authentication.",
    )
    password: EncryptedStr = Field(
        title="Secret key or Password",
        description="The secret key or password to use for authentication.",
    )
    prisma_id: str = Field(
        default_factory=str,
        title="Prisma ID",
        description="Prisma ID is needed if your username is for a multi-tenant user.",
    )


class PaloAltoPrismaCloudV1Connection(ConnectionTemplate):
    id = "palo_alto_prisma_cloud"
    name = "Palo Alto Prisma Cloud"
    config_model = PaloAltoPrismaCloudV1Config
