from requests import HTTPError

from apps.connectors.integrations.health_check import (
    IntegrationConnectionHealthCheck,
    IntegrationHealthCheckResult,
)
from apps.connectors.integrations.vendors.palo_alto.palo_alto_prisma_cloud.v1.api import (
    PaloAltoPrismaCloudV1Api,
)


class ConnectionHealthCheck(IntegrationConnectionHealthCheck):
    def get_result(self) -> IntegrationHealthCheckResult:
        try:
            api: PaloAltoPrismaCloudV1Api = self.integration.get_api()
            api.api_client
            return IntegrationHealthCheckResult.PASSED
        except HTTPError:
            return IntegrationHealthCheckResult.FAILED
