import datetime

from apps.connectors.integrations.api import ApiBase


def paginate(func, **kwargs):
    """
    Generator to paginate through results
    """
    response = func(**kwargs)
    items_count = 0
    while True:
        items = response.get("items", [])
        items_count = len(items)
        next_page_token = response.get("nextPageToken")
        if items_count == 0 or not next_page_token:
            break
        yield from items
        kwargs["page_token"] = next_page_token
        response = func(**kwargs)


class PaloAltoPrismaCloudV1Api(ApiBase):
    def __init__(
        self,
        base_url=None,
        username=None,
        password=None,
        prisma_id=None,
    ):
        self.base_url = base_url
        self.prisma_id = prisma_id
        self.username = username
        self.password = password
        self.token = None
        self.token_expiry = None
        super().__init__(base_url=base_url)

    @property
    def api_client(self):
        if not self.token or datetime.datetime.now() >= self.token_expiry:
            self.token_expiry = datetime.datetime.now() + datetime.timedelta(minutes=10)
            url = f"{self.base_url}/login"
            headers = {
                "Content-Type": "application/json; charset=UTF-8",
                "Accept": "application/json; charset=UTF-8",
            }

            payload = {
                "username": self.username,
                "password": self.password,
            }
            if self.prisma_id:
                payload["prismaId"] = self.prisma_id
            response = self.session.post(url, json=payload, headers=headers)
            data = response.json()
            self.token = data.get("token")
            self.session.headers.update(
                {
                    "x-redlock-auth": self.token,
                    "Accept": "application/json",
                    "Content-Type": "application/json",
                }
            )
        return self.session

    def list_alerts(
        self, time_amount=100, time_unit="minute", detailed=True, page_token=None
    ):
        """
        List alerts from Prisma Cloud.
        """
        url = f"{self.base_url}/v2/alert"
        params = {
            "timeType": "relative",
            "timeAmount": time_amount,
            "timeUnit": time_unit,
            "detailed": str(detailed).lower(),
        }
        if page_token:
            params["pageToken"] = page_token
        response = self.api_client.get(url, params=params)
        return response.json()

    def get_alert_detail(self, alert_id):
        """
        Get details of a specific alert.
        """
        url = f"{self.base_url}/v2/alert/{alert_id}"
        response = self.api_client.get(url)
        return response.json()

    def dismiss_alert(self, alert_id):
        """
        Dismiss a specific alert.
        """
        url = f"{self.base_url}/alert/dismiss"
        payload = {
            "alerts": [alert_id],
        }
        response = self.api_client.post(url, json=payload)
        return response.json()

    def reopen_alert(self, alert_id):
        """
        Reopen a specific alert.
        """
        url = f"{self.base_url}/alert/reopen"
        payload = {
            "alerts": [alert_id],
        }
        response = self.api_client.post(url, json=payload)
        return response.json()
