from apps.connectors.integrations import TemplateVersion

from .bookmarks import PaloAltoPrismaCloudEventSyncBookmarks
from .connection import (
    PaloAltoPrismaCloudV1Config,
    PaloAltoPrismaCloudV1Connection,
)
from .integration import PaloAltoPrismaCloudV1Integration
from .settings import PaloAltoPrismaCloudV1Settings


class PaloAltoPrismaCloudV1TemplateVersion(TemplateVersion):
    integration = PaloAltoPrismaCloudV1Integration
    id = "v1"
    name = "v1"
    config_model = PaloAltoPrismaCloudV1Config
    settings_model = PaloAltoPrismaCloudV1Settings
    connection_model = PaloAltoPrismaCloudV1Connection
    bookmarks_model = PaloAltoPrismaCloudEventSyncBookmarks
