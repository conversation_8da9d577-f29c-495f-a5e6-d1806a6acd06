from apps.connectors.integrations import Integration

from .actions.event_sync import PaloAltoPrismaCloudV1EventSync
from .actions.update_lifecycle_status import PaloAltoPrismaCloudV1UpdateLifecycleStatus
from .api import PaloAltoPrismaCloudV1Api
from .health_check import ConnectionHealthCheck


class PaloAltoPrismaCloudV1Integration(Integration):
    api_class = PaloAltoPrismaCloudV1Api
    actions = (
        PaloAltoPrismaCloudV1EventSync,
        PaloAltoPrismaCloudV1UpdateLifecycleStatus,
    )
    critical_health_checks = (ConnectionHealthCheck,)
