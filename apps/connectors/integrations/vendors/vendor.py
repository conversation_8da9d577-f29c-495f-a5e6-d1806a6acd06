from dataclasses import dataclass


class Vendors:
    @dataclass(frozen=True)
    class Vendor:
        id: str
        name: str

    ABNORMAL = Vendor("abnormal", "Abnormal")
    AKAMAI = Vendor("akamai", "Akamai")
    AMAZON = Vendor("amazon", "Amazon")
    ABSOLUTE = Vendor("absolute", "Absolute")
    ABUSE_IPDB = Vendor("abuse_ipdb", "AbuseIPDB")
    CHECK_POINT = Vendor("check_point", "Check Point")
    CISCO = Vendor("cisco", "Cisco")
    CLAROTY = Vendor("claroty", "Claroty")
    COMMVAULT = Vendor("commvault", "Commvault")
    CRITICALSTART = Vendor("criticalstart", "Critical Start")
    CROWDSTRIKE = Vendor("crowdstrike", "CrowdStrike")
    CYBERARK = Vendor("cyberark", "CyberArk")
    CYLANCE = Vendor("cylance", "Cylance")
    DEVO = Vendor("devo", "Devo")
    EXTRAHOP = Vendor("extrahop", "ExtraHop")
    FORCEPOINT = Vendor("forcepoint", "Forcepoint")
    FORTINET = Vendor("fortinet", "Fortinet")
    FRESHWORKS = Vendor("freshworks", "Freshworks")
    GOOGLE = Vendor("google", "Google")
    IMPORT = Vendor("import", "Import")
    INFOBLOX = Vendor("infoblox", "Infoblox")
    IVANTI = Vendor("ivanti", "Ivanti")
    JAMF = Vendor("jamf", "Jamf")
    LANSWEEPER = Vendor("lansweeper", "Lansweeper")
    MICROSOFT = Vendor("microsoft", "Microsoft")
    MIMECAST = Vendor("mimecast", "Mimecast")
    NETAPP = Vendor("netapp", "NetApp")
    NETSKOPE = Vendor("netskope", "Netskope")
    NOZOMI_VANTAGE = Vendor("nozomi_vantage", "Nozomi Networks")
    OKTA = Vendor("okta", "Okta")
    ONELOGIN = Vendor("onelogin", "OneLogin")
    OTORIO = Vendor("otorio", "OTORIO")
    PALO_ALTO = Vendor("palo_alto", "Palo Alto Networks")
    PROOFPOINT = Vendor("proofpoint", "Proofpoint")
    QUALYS = Vendor("qualys", "Qualys")
    RAPID7 = Vendor("rapid7", "Rapid7")
    RSA_SECURITY = Vendor("rsa_security", "RSA Security")
    SENTINELONE = Vendor("sentinel_one", "SentinelOne")
    SERVICENOW = Vendor("service_now", "ServiceNow")
    SEVCO = Vendor("sevco", "Sevco Security")
    SOLARWINDS = Vendor("solarwinds", "SolarWinds")
    SPLUNK = Vendor("splunk", "Splunk")
    SUMO_LOGIC = Vendor("sumo_logic", "Sumo Logic")
    TANIUM = Vendor("tanium", "Tanium")
    TENABLE = Vendor("tenable", "Tenable")
    TREND_MICRO = Vendor("trend_micro", "Trend Micro")
    UBIQUITI = Vendor("ubiquiti", "Ubiquiti")
    VECTRA_AI = Vendor("vectra_ai", "Vectra AI")
    VEEAM = Vendor("veeam", "Veeam")
    VERITAS = Vendor("veritas", "Veritas")
    VMWARE = Vendor("vmware", "VMware")
    VULNCHECK = Vendor("vulncheck", "VulnCheck")
    WIZ = Vendor("wiz", "Wiz")
    ZSCALER = Vendor("zscaler", "Zscaler")

    @classmethod
    def get_all_vendors(cls):
        return [
            value
            for value in Vendors.__dict__.values()
            if isinstance(value, Vendors.Vendor)
        ]
