import base64
import json
from datetime import datetime, timezone
from http import HTTPStatus
from unittest.mock import patch

import responses

from apps.connectors.health_checks.components.component import (
    HealthCheckComponent,
    HealthCheckRequirement,
    RequirementStatus,
    ValidationStatus,
)
from apps.connectors.integrations import IntegrationActionType
from apps.connectors.integrations.health_check import IntegrationHealthCheckResult
from apps.connectors.integrations.vendors.microsoft.ms_xdr.v1.actions.event_sync import (
    _normalize_iot_device,
)
from apps.connectors.integrations.vendors.microsoft.ms_xdr.v1.bookmarks import (
    MsXdrV1Bookmarks,
)
from apps.connectors.integrations.vendors.microsoft.ms_xdr.v1.health_check import (
    <PERSON>Health<PERSON>he<PERSON>,
    ReadAlerts,
)
from apps.connectors.tests.integrations.base import (
    BaseIntegrationTest,
    HealthCheckComponentTestMixin,
)
from apps.connectors.utils import serialize
from apps.tests.base import BaseTestCase
from factories import ConnectorFactory
from factories.aad_app import AadApp<PERSON>actory


def format_token(access_token):
    # Format as JWT token
    access_token_bytes = json.dumps(access_token).encode("utf-8")
    access_token_base64 = base64.b64encode(access_token_bytes).decode()
    # no need to set up a proper header and signature, since they are not verified
    # by the library
    return f"header.{access_token_base64}.signature"


def setup_oauth_responses(tenant_id, roles: list = None):
    if roles is None:
        roles = [
            "SecurityAlert.ReadWrite.All",
            "Directory.Read.All",
            "CustomDetection.ReadWrite.All",
            "ThreatHunting.Read.All",
            "Machine.Isolate",
            "Machine.Read.All",
            "Machine.ReadWrite.All",
        ]
    access_token = {
        "aud": "https://graph.microsoft.com",
        "aio": "E2FgYNCtS2B6fdLpYGHq4sKWlEnmAA==",
        "app_displayname": "Critical Start MDR - Azure Sentinel",
        "roles": roles,
    }
    responses.post(
        f"https://login.microsoftonline.com/{tenant_id}/oauth2/v2.0/token",
        json={
            "access_token": format_token(access_token),
            "expires_in": 3600,
            "token_type": "Bearer",
        },
    )


def setup_detection_rule_responses(tenant_id: str, roles: list = None) -> None:
    setup_oauth_responses(tenant_id, roles)

    responses.post(
        f"https://graph.microsoft.com/beta/security/rules/detectionRules",
        json={"detectorId": "foo"},
    )

    responses.patch(
        f"https://graph.microsoft.com/beta/security/rules/detectionRules/7506",
        json={},
    )

    responses.delete(
        f"https://graph.microsoft.com/beta/security/rules/detectionRules/7506",
        json={},
    )

    responses.get(
        f"https://graph.microsoft.com/beta/security/rules/detectionRules",
        json={
            "value": [
                {
                    "@odata.type": "#microsoft.graph.security.detectionRule",
                    "id": "7506",
                    "displayName": "ban file",
                    "isEnabled": True,
                    "createdBy": "<EMAIL>",
                    "createdDateTime": "2021-02-28T16:28:15.3863467Z",
                    "lastModifiedDateTime": "2023-05-24T09:26:11.8630516Z",
                    "lastModifiedBy": "<EMAIL>",
                    "detectorId": "67895317-b2a8-4ac3-8f8b-fa6b7765f2fe",
                    "queryCondition": {
                        "queryText": 'DeviceFileEvents\r\n| where Timestamp > ago(1h)\r\n| where FileName == "ifz30zlx.dll"',
                        "lastModifiedDateTime": None,
                    },
                    "schedule": {
                        "period": "24H",
                        "nextRunDateTime": "2023-06-26T08:52:06.1766667Z",
                    },
                    "lastRunDetails": {
                        "lastRunDateTime": "2023-06-25T08:52:06.1766667Z",
                        "status": None,
                        "failureReason": None,
                        "errorCode": None,
                    },
                    "detectionAction": {
                        "alertTemplate": {
                            "title": "unwanted dll",
                            "description": "test",
                            "severity": "low",
                            "category": "Malware",
                            "recommendedActions": None,
                            "mitreTechniques": [],
                            "impactedAssets": [],
                        },
                        "organizationalScope": None,
                        "responseActions": [
                            {
                                "@odata.type": "#microsoft.graph.security.restrictAppExecutionResponseAction",
                                "identifier": "deviceId",
                            },
                            {
                                "@odata.type": "#microsoft.graph.security.initiateInvestigationResponseAction",
                                "identifier": "deviceId",
                            },
                            {
                                "@odata.type": "#microsoft.graph.security.collectInvestigationPackageResponseAction",
                                "identifier": "deviceId",
                            },
                            {
                                "@odata.type": "#microsoft.graph.security.runAntivirusScanResponseAction",
                                "identifier": "deviceId",
                            },
                            {
                                "@odata.type": "#microsoft.graph.security.isolateDeviceResponseAction",
                                "isolationType": "full",
                                "identifier": "deviceId",
                            },
                            {
                                "@odata.type": "#microsoft.graph.security.blockFileResponseAction",
                                "identifier": "sha1",
                                "deviceGroupNames": [],
                            },
                        ],
                    },
                },
                {
                    "@odata.type": "#microsoft.graph.security.detectionRule",
                    "id": "8575",
                    "displayName": "Continuous_EmailAttachmentInfo_Mod300",
                    "isEnabled": True,
                    "createdBy": "<EMAIL>",
                    "createdDateTime": "2021-11-03T21:32:01.6144651Z",
                    "lastModifiedDateTime": "2022-11-03T19:27:14.4187141Z",
                    "lastModifiedBy": "<EMAIL>",
                    "detectorId": "56ef4994-fe31-4ac9-b29f-0ca2f2cc9112",
                    "queryCondition": {
                        "queryText": "EmailAttachmentInfo\r\n| extend second = datetime_diff('second',now(),Timestamp)\r\n| where second % 300 == 0 ",
                        "lastModifiedDateTime": "2022-11-03T19:27:14.4331537Z",
                    },
                    "schedule": {
                        "period": "0",
                        "nextRunDateTime": "2021-11-03T21:32:01.7863185Z",
                    },
                    "lastRunDetails": {
                        "lastRunDateTime": "2021-11-03T21:32:01.7863185Z",
                        "status": None,
                        "failureReason": None,
                        "errorCode": None,
                    },
                    "detectionAction": {
                        "alertTemplate": {
                            "title": "EmailAttachmentInfo",
                            "description": "EmailAttachmentInfo",
                            "severity": "low",
                            "category": "Exfiltration",
                            "recommendedActions": "EmailAttachmentInfo",
                            "mitreTechniques": [],
                            "impactedAssets": [
                                {
                                    "@odata.type": "#microsoft.graph.security.impactedMailboxAsset",
                                    "identifier": "recipientEmailAddress",
                                },
                                {
                                    "@odata.type": "#microsoft.graph.security.impactedUserAsset",
                                    "identifier": "recipientObjectId",
                                },
                            ],
                        },
                        "organizationalScope": None,
                        "responseActions": [
                            {
                                "@odata.type": "#microsoft.graph.security.moveToDeletedItemsResponseAction",
                                "identifier": "networkMessageId, recipientEmailAddress",
                            }
                        ],
                    },
                },
                {
                    "@odata.type": "#microsoft.graph.security.detectionRule",
                    "id": "9794",
                    "displayName": "UPDATED DET: Office/LoLBin Network Connection to Low-Reputation TLD",
                    "isEnabled": None,
                    "createdBy": "<EMAIL>",
                    "createdDateTime": "2022-02-02T10:26:01.7708581Z",
                    "lastModifiedDateTime": "2022-02-02T10:26:01.7708581Z",
                    "lastModifiedBy": "<EMAIL>",
                    "detectorId": "67aa92a1-b04b-4f2a-a223-236968a3da96",
                    "queryCondition": {
                        "queryText": '//https://www.spamhaus.org/statistics/tlds/      http://www.surbl.org/tld       https://www.iana.org/domains/root/db      https://unit42.paloaltonetworks.com/top-level-domains-cybercrime/\r\nDeviceNetworkEvents\r\n| where isnotempty(RemoteUrl) and RemoteIPType == "Public"\r\n| where InitiatingProcessFileName in~ ("winword.exe", "excel.exe", "powerpnt.exe", "rundll32.exe", "regsvr32.exe", "certutil.exe", "bitsadmin.exe", "wscript.exe", "cscript.exe", "powershell.exe", "pwsh.exe", "powershell_ise.exe")\r\n| extend TopLevelDomain=tolower(extract(@"([A-Za-z0-9-]{1,63}\\.)+([A-Za-z]{2,10})", 2, RemoteUrl))\r\n| where TopLevelDomain in ("xyz", "top", "live", "loan", "club", "surf", "work", "biz", "ryukyu", "press", "ltd", "bid", "vip", "online", "download" "buzz", "cam", "ru", "cn", "ci", "ga", "gq", "tk", "tw", "ml", "cf", "cfd", "icu", "cm")\r\n| extend TimeDiff=datetime_diff("Second", Timestamp, InitiatingProcessCreationTime)\r\n| where TimeDiff < 30\r\n| project-reorder Timestamp, DeviceName, RemoteUrl, TopLevelDomain, TimeDiff, InitiatingProcessCommandLine, *\r\n//| summarize count() by InitiatingProcessFolderPath, TopLevelDomain, RemoteUrl',
                        "lastModifiedDateTime": None,
                    },
                    "schedule": {
                        "period": "1H",
                        "nextRunDateTime": "2023-06-25T10:17:06.4366667Z",
                    },
                    "lastRunDetails": {
                        "lastRunDateTime": "2023-06-25T09:17:06.4366667Z",
                        "status": None,
                        "failureReason": None,
                        "errorCode": None,
                    },
                    "detectionAction": {
                        "alertTemplate": {
                            "title": "updated Office/LoLBin Network Connection to Low-Reputation TLD",
                            "description": "This is a custom detection created by the Centene Detection Engineering team.\n\nAn Office application or Living-Off-The-Land Binary made an immediate remote connection to a domain with a low-reputation top level domain after execution. This activity is suspicious as threat actors typically use low-reputation TLDs for malicious purposes, such as hosting payloads for potential targets. These TLDs are often abused because of their low cost and lack of oversite. The TLDs included in the list cover destinations that have either a high count or a high percentage of low-reputation sites. ",
                            "severity": "low",
                            "category": "CommandAndControl",
                            "recommendedActions": "Check the reputation of the RemoteUrl through OSINT tools such as VirusTotal and Hybrid Analysis.\n\nReview the document and device timeline for additional context and IOCs. \n\nCheck for related alerts on the associated endpoint. ",
                            "mitreTechniques": ["T1071.001"],
                            "impactedAssets": [
                                {
                                    "@odata.type": "#microsoft.graph.security.impactedDeviceAsset",
                                    "identifier": "deviceId",
                                }
                            ],
                        },
                        "organizationalScope": {
                            "scopeType": "deviceGroup",
                            "scopeNames": ["UnassignedGroup"],
                        },
                        "responseActions": [],
                    },
                },
            ]
        },
    )


def setup_data_sources_responses(tenant_id: str, roles: list = None) -> None:
    setup_oauth_responses(tenant_id, roles)

    responses.post(
        f"https://graph.microsoft.com/v1.0/security/runHuntingQuery",
        json={
            "results": [
                {
                    "TableName": "SecurityAlert",
                    "LastLogTime": "2024-03-26T09:39:50.7688641Z",
                    "Count": 1,
                },
                {
                    "TableName": "SecurityIncident",
                    "LastLogTime": "2024-03-26T09:39:49.4353788Z",
                    "Count": 2,
                },
            ]
        },
    )


def setup_get_alerts_response(tenant_id: str) -> None:
    setup_oauth_responses(tenant_id)

    responses.get(
        f"https://graph.microsoft.com/v1.0/security/alerts_v2",
        json={
            "value": [
                {
                    "id": "dacfc033fc-8735-43e6-94e3-7c107cb8137e_1",
                    "providerAlertId": "cfc033fc-8735-43e6-94e3-7c107cb8137e_1",
                    "incidentId": "123",
                    "status": "resolved",
                    "severity": "high",
                    "classification": "truePositive",
                    "determination": None,
                    "serviceSource": "microsoftDefenderForEndpoint",
                    "detectionSource": "antivirus",
                    "productName": "Microsoft Defender for Endpoint",
                    "detectorId": "8cac979e-6dcc-44ad-8355-480eebaff1c1",
                    "tenantId": "67895317-b2a8-4ac3-8f8b-fa6b7765f2fe",
                    "title": "Mimikatz credential theft tool",
                    "description": "The Mimikatz hacktool was detected on this device. Mimikatz is a credential theft tool that can harvest plaintext passwords, password hashes, smartcard PINs, and Kerberos tickets.  An attacker might be trying to harvest credentials to log into this or other devices on the network, by impersonating a valid user.\n\nFor more information, read the Mimikatz tool profile Threat Analytics report: https://security.microsoft.com/threatanalytics3/51db19ab-081f-46f3-9fe1-3f5e5d2f8ee0/analystreport? and read the Actor profile: Storm-0875 Threat Analytics report https://security.microsoft.com/threatanalytics3/4f60801d-912e-4bcb-91b2-f46879c8a718/analystreport\n",
                    "recommendedActions": "A. Validate the alert and scope the suspected breach.\n1. Find related machines, network addresses, and files in the incident graph.\n2. Check for other suspicious activities in the machine timeline.?\n3. Locate unfamiliar processes in the process tree. Check files for prevalence, their locations, and digital signatures.?4. Submit relevant files for deep analysis and review file behaviors.\n?5. Identify unusual system activity with system owners.\n\n?B. If you have validated the alert, contain and mitigate the breach.?\n1. Record relevant artifacts, including those you need in mitigation rules.\n?2. Stop suspicious processes. Block prevalent malware files across the network.\n\nC. Contact your incident response team, or contact Microsoft support for investigation and remediation services.",
                    "category": "CredentialAccess",
                    "assignedTo": "<EMAIL>",
                    "alertWebUrl": "https://security.microsoft.com/alerts/dacfc033fc-8735-43e6-94e3-7c107cb8137e_1?tid=67895317-b2a8-4ac3-8f8b-fa6b7765f2fe",
                    "incidentWebUrl": "https://security.microsoft.com/incidents/544176?tid=67895317-b2a8-4ac3-8f8b-fa6b7765f2fe",
                    "actorDisplayName": None,
                    "threatDisplayName": "HackTool:Win32/Mimikatz.I",
                    "threatFamilyName": "Mimikatz",
                    "mitreTechniques": [],
                    "createdDateTime": datetime.now(timezone.utc)
                    .isoformat()
                    .replace("+00:00", "Z"),
                    "lastUpdateDateTime": datetime.now(timezone.utc)
                    .isoformat()
                    .replace("+00:00", "Z"),
                    "resolvedDateTime": "2024-09-02T12:59:21.2086656Z",
                    "firstActivityDateTime": "2024-09-01T16:49:10.9566532Z",
                    "lastActivityDateTime": "2024-09-01T16:52:35.4976305Z",
                    "systemTags": [],
                    "alertPolicyId": None,
                    "additionalData": None,
                    "evidence": [
                        {
                            "@odata.type": "#microsoft.graph.security.deviceEvidence",
                            "createdDateTime": "2024-09-01T16:50:25.6766667Z",
                            "verdict": "unknown",
                            "remediationStatus": "none",
                            "remediationStatusDetails": None,
                            "roles": [],
                            "detailedRoles": ["PrimaryDevice"],
                            "tags": [],
                            "firstSeenDateTime": "2023-11-06T18:09:02.4306508Z",
                            "mdeDeviceId": "a74c358887232309c7dce7c13759093ae6a5d2e8",
                            "azureAdDeviceId": "8f21c940-3ee6-4e16-83bc-a54a464a6201",
                            "deviceDnsName": "fake.criticalstart.com",
                            "osPlatform": "Windows11",
                            "osBuild": 22631,
                            "version": "23H2",
                            "healthStatus": "active",
                            "riskScore": "none",
                            "rbacGroupId": 247,
                            "rbacGroupName": "UnassignedGroup",
                            "onboardingStatus": "onboarded",
                            "defenderAvStatus": "notSupported",
                            "lastIpAddress": "127.0.0.1",
                            "lastExternalIpAddress": "**********",
                            "ipInterfaces": ["127.0.0.1", "::1", "************"],
                            "vmMetadata": None,
                            "loggedOnUsers": [],
                        },
                        {
                            "@odata.type": "#microsoft.graph.security.userEvidence",
                            "createdDateTime": "2024-09-01T16:50:25.6766667Z",
                            "verdict": "unknown",
                            "remediationStatus": "none",
                            "remediationStatusDetails": None,
                            "roles": [],
                            "detailedRoles": [],
                            "tags": [],
                            "stream": None,
                            "userAccount": {
                                "accountName": "fake",
                                "domainName": "GLOBAL",
                                "userSid": "FAKE-SID",
                                "azureAdUserId": "dc32ac31-0136-426e-afe6-86f509e23fd9",
                                "userPrincipalName": "<EMAIL>",
                                "displayName": None,
                            },
                        },
                        {
                            "@odata.type": "#microsoft.graph.security.processEvidence",
                            "createdDateTime": "2024-09-01T16:50:25.6766667Z",
                            "verdict": "suspicious",
                            "remediationStatus": "none",
                            "remediationStatusDetails": None,
                            "roles": [],
                            "detailedRoles": [],
                            "tags": [],
                            "processId": 20000,
                            "parentProcessId": 15824,
                            "processCommandLine": '"NaturalReader 16.exe" --type=renderer --disable-features=SpareRendererForSitePerProcess --service-pipe-token=********** --lang=en-US --app-path="C:\\Program Files (x86)\\Naturalsoft\\naturalreader16\\resources\\app" --node-integration --no-sandbox --no-zygote --background-color=#fff --device-scale-factor=1 --num-raster-threads=4 --enable-main-frame-before-activation --service-request-channel-token=********** --renderer-client-id=4 --no-v8-untrusted-code-mitigations --mojo-platform-channel-handle=2464 /prefetch:1',
                            "processCreationDateTime": "2024-09-01T15:43:19.5454244Z",
                            "parentProcessCreationDateTime": "2024-09-01T15:43:19.2332371Z",
                            "detectionStatus": "blocked",
                            "mdeDeviceId": "a74c358887232309c7dce7c13759093ae6a5d2e8",
                            "imageFile": {
                                "sha1": "630bf2f09773dc52711f830ab3f865966295c5f5",
                                "sha256": "6eeb42d3d1e138948cc8ce6ac42e8cfc2a989aac19b6d7528319e83a9e750c67",
                                "fileName": "NaturalReader 16.exe",
                                "filePath": "C:\\Program Files (x86)\\Naturalsoft\\naturalreader16",
                                "fileSize": ********,
                                "filePublisher": "GitHub, Inc.",
                                "signer": None,
                                "issuer": None,
                            },
                            "parentProcessImageFile": {
                                "sha1": None,
                                "sha256": None,
                                "fileName": "NaturalReader 16.exe",
                                "filePath": "C:\\Program Files (x86)\\Naturalsoft\\naturalreader16",
                                "fileSize": ********,
                                "filePublisher": "GitHub, Inc.",
                                "signer": None,
                                "issuer": None,
                            },
                            "userAccount": {
                                "accountName": "fake",
                                "domainName": "GLOBAL",
                                "userSid": "FAKE-SID",
                                "azureAdUserId": "dc32ac31-0136-426e-afe6-86f509e23fd9",
                                "userPrincipalName": "<EMAIL>",
                                "displayName": None,
                            },
                        },
                        {
                            "@odata.type": "#microsoft.graph.security.processEvidence",
                            "createdDateTime": "2024-09-01T16:53:58.88Z",
                            "verdict": "suspicious",
                            "remediationStatus": "none",
                            "remediationStatusDetails": None,
                            "roles": [],
                            "detailedRoles": [],
                            "tags": [],
                            "processId": 15824,
                            "parentProcessId": 22280,
                            "processCommandLine": '"NaturalReader 16.exe" --type=renderer --disable-features=SpareRendererForSitePerProcess --service-pipe-token=********** --lang=en-US --app-path="C:\\Program Files (x86)\\Naturalsoft\\naturalreader16\\resources\\app" --node-integration --no-sandbox --no-zygote --background-color=#fff --device-scale-factor=1 --num-raster-threads=4 --enable-main-frame-before-activation --service-request-channel-token=********** --renderer-client-id=4 --no-v8-untrusted-code-mitigations --mojo-platform-channel-handle=2452 /prefetch:1',
                            "processCreationDateTime": "2024-09-01T16:51:06.4067935Z",
                            "parentProcessCreationDateTime": "2024-09-01T16:51:06.0669791Z",
                            "detectionStatus": "blocked",
                            "mdeDeviceId": "a74c358887232309c7dce7c13759093ae6a5d2e8",
                            "imageFile": {
                                "sha1": "630bf2f09773dc52711f830ab3f865966295c5f5",
                                "sha256": "6eeb42d3d1e138948cc8ce6ac42e8cfc2a989aac19b6d7528319e83a9e750c67",
                                "md5": "b0c3f1a2d4e6f8e7c9a2b8e4d4e6f8e7",
                                "fileName": "NaturalReader 16.exe",
                                "filePath": "C:\\Program Files (x86)\\Naturalsoft\\naturalreader16",
                                "fileSize": ********,
                                "filePublisher": "GitHub, Inc.",
                                "signer": None,
                                "issuer": None,
                            },
                            "parentProcessImageFile": {
                                "sha1": None,
                                "sha256": None,
                                "fileName": "NaturalReader 16.exe",
                                "filePath": "\\Device\\HarddiskVolume3\\Program Files (x86)\\Naturalsoft\\naturalreader16",
                                "fileSize": None,
                                "filePublisher": None,
                                "signer": None,
                                "issuer": None,
                            },
                            "userAccount": {
                                "accountName": "fake",
                                "domainName": "GLOBAL",
                                "userSid": "FAKE-SID",
                                "azureAdUserId": "dc32ac31-0136-426e-afe6-86f509e23fd9",
                                "userPrincipalName": "<EMAIL>",
                                "displayName": None,
                            },
                        },
                        {
                            "@odata.type": "#microsoft.graph.security.analyzedMessageEvidence",
                            "createdDateTime": "2024-09-03T20:23:11.2233333Z",
                            "verdict": "unknown",
                            "remediationStatus": "none",
                            "remediationStatusDetails": None,
                            "roles": [],
                            "detailedRoles": [],
                            "tags": [],
                            "networkMessageId": "4f014fc5-529f-4f4a-97ae-b49a5e61a8ce",
                            "internetMessageId": "<<EMAIL>>",
                            "subject": "Re: Gilles Venturi - HiFIT vous contacte ! Dur?e de vie plus longue pour vos structures?soud?es",
                            "language": "fr",
                            "senderIp": "127.0.0.1",
                            "recipientEmailAddress": "<EMAIL>",
                            "antiSpamDirection": None,
                            "deliveryAction": "delivered",
                            "deliveryLocation": "inbox",
                            "urn": "urn:MailEntity:fake",
                            "threats": ["ZapPhish", "HighConfPhish"],
                            "threatDetectionMethods": [],
                            "urls": [
                                "https://fake.com/foo",
                            ],
                            "urlCount": 1,
                            "attachmentsCount": 0,
                            "receivedDateTime": "2024-09-03T13:50:00Z",
                            "p1Sender": {
                                "emailAddress": "<EMAIL>",
                                "displayName": None,
                                "domainName": "fake.com",
                            },
                            "p2Sender": {
                                "emailAddress": "<EMAIL>",
                                "displayName": None,
                                "domainName": "fake.com",
                            },
                        },
                        {
                            "@odata.type": "#microsoft.graph.security.urlEvidence",
                            "createdDateTime": "2025-01-20T10:39:34.58Z",
                            "verdict": "unknown",
                            "remediationStatus": "none",
                            "remediationStatusDetails": None,
                            "roles": [],
                            "detailedRoles": [],
                            "tags": [],
                            "url": "https://fake.com",
                        },
                        {
                            "@odata.type": "#microsoft.graph.security.registryKeyEvidence",
                            "createdDateTime": "2025-01-11T11:54:51.45Z",
                            "verdict": "suspicious",
                            "remediationStatus": "none",
                            "remediationStatusDetails": None,
                            "roles": [],
                            "detailedRoles": [],
                            "tags": [],
                            "registryKey": "SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion\\Schedule\\TaskCache\\Tasks\\{1D5B8CFA-CC30-40F6-BEE7-7AC69D1096A8}",
                            "registryHive": "HKEY_LOCAL_MACHINE",
                        },
                        {
                            "@odata.type": "#microsoft.graph.security.registryValueEvidence",
                            "createdDateTime": "2024-11-04T20:01:52.6433333Z",
                            "verdict": "suspicious",
                            "remediationStatus": "active",
                            "remediationStatusDetails": None,
                            "roles": [],
                            "detailedRoles": [],
                            "tags": [],
                            "mdeDeviceId": "fake",
                            "registryKey": "SYSTEM\\ControlSet001\\Control\\Lsa",
                            "registryHive": "HKEY_LOCAL_MACHINE",
                            "registryValue": "72-00-61-00-73-00-73-00-66-00-6D-00-00-00-73-00-63-00-65-00-63-00-6C-00-69-00-00-00-00-00",
                            "registryValueName": "Notification Packages",
                            "registryValueType": "MultiString",
                        },
                        {
                            "@odata.type": "#microsoft.graph.security.fileEvidence",
                            "createdDateTime": "2024-10-29T13:46:33.2Z",
                            "verdict": "suspicious",
                            "remediationStatus": "prevented",
                            "remediationStatusDetails": "Entity was pre-remediated by Windows Defender",
                            "roles": [],
                            "detailedRoles": [],
                            "tags": [],
                            "detectionStatus": "prevented",
                            "mdeDeviceId": "fake",
                            "fileDetails": {
                                "sha1": "99ed6135defff6e675d626f742389d6280abdb60",
                                "sha256": "76491df69a26019139ac11117cd21bf5d0257a5ebd3d67837f558c8c9c3483d8",
                                "fileName": "FileZilla_3.67.1_win64_sponsored2-setup.exe",
                                "filePath": "C:\\Users\\<USER>\\Downloads",
                                "fileSize": 12826192,
                                "filePublisher": None,
                                "signer": None,
                                "issuer": None,
                            },
                        },
                        {
                            "@odata.type": "#microsoft.graph.security.cloudApplicationEvidence",
                            "createdDateTime": "2024-10-27T13:46:24.0533333Z",
                            "verdict": "unknown",
                            "remediationStatus": "none",
                            "remediationStatusDetails": None,
                            "roles": [],
                            "detailedRoles": [],
                            "tags": [],
                            "appId": 11161,
                            "displayName": "Microsoft 365",
                            "instanceId": 0,
                            "instanceName": "Microsoft 365",
                            "saasAppId": 11161,
                            "stream": None,
                        },
                        {
                            "@odata.type": "FAKE",
                            "createdDateTime": "2024-10-27T13:46:24.0533333Z",
                        },
                    ],
                }
            ]
        },
    )


def setup_get_incidents_response(tenant_id: str) -> None:
    setup_oauth_responses(tenant_id)

    responses.get(
        f"https://graph.microsoft.com/v1.0/security/incidents",
        json={
            "value": [
                {
                    "id": "123",
                    "lastUpdateDateTime": datetime.now(timezone.utc)
                    .isoformat()
                    .replace("+00:00", "Z"),
                    "createdDateTime": "2024-10-27T13:46:24.0533333Z",
                }
            ]
        },
    )


def setup_update_alert_response(tenant_id: str, alert_id: str) -> None:
    setup_oauth_responses(tenant_id)

    responses.patch(
        f"https://graph.microsoft.com/v1.0/security/alerts_v2/{alert_id}",
        json={},
    )

    responses.post(
        f"https://graph.microsoft.com/v1.0/security/alerts_v2/{alert_id}/comments",
        json={},
    )


def setup_add_alert_comment_response(tenant_id: str, alert_id: str) -> None:
    setup_oauth_responses(tenant_id)

    responses.post(
        f"https://graph.microsoft.com/v1.0/security/incidents/{alert_id}/comments",
        json={},
    )


def get_ms_xdr_events_artifact_lines():
    artifact = [
        {
            "raw_event": {
                "serviceSource": "microsoftDefenderForOffice365",
                "detectorId": "detectorIdtest",
                "title": "titletest",
            },
            "event_timestamp": "2025-03-24T14:36:29.647818Z",
            "ioc": {
                "external_id": "TEST",
                "external_name": "test alert",
                "has_ioc_definition": True,
                "mitre_techniques": ["foo"],
            },
            "vendor_item_ref": {
                "id": "123",
                "title": "test MDO alert",
                "alternate_id": None,
                "url": "",
                "created": "2025-03-24T14:36:29.647808Z",
            },
        },
        {
            "raw_event": {
                "serviceSource": "microsoftSentinel",
            },
            "event_timestamp": "2025-03-24T14:36:29.647818Z",
            "ioc": {
                "external_id": "TEST",
                "external_name": "test alert",
                "has_ioc_definition": True,
                "mitre_techniques": ["foo"],
            },
            "vendor_item_ref": {
                "id": "456",
                "title": "test Sentinel alert",
                "alternate_id": None,
                "url": "",
                "created": "2025-03-24T14:36:29.647808Z",
            },
        },
        {
            "raw_event": {
                "serviceSource": "microsoftDefenderForEndpoint",
            },
            "event_timestamp": "2025-03-24T14:36:29.647818Z",
            "ioc": {
                "external_id": "TEST",
                "external_name": "test alert",
                "has_ioc_definition": True,
                "mitre_techniques": ["foo"],
            },
            "vendor_item_ref": {
                "id": "456",
                "title": "test MDE alert",
                "alternate_id": None,
                "url": "",
                "created": "2025-03-24T14:36:29.647808Z",
            },
        },
        {
            "raw_event": {
                "serviceSource": "microsoftDefenderForIdentity",
            },
            "event_timestamp": "2025-03-24T14:36:29.647818Z",
            "ioc": {
                "external_id": "TEST",
                "external_name": "test alert",
                "has_ioc_definition": True,
                "mitre_techniques": ["foo"],
            },
            "vendor_item_ref": {
                "id": "456",
                "title": "test MDI alert",
                "alternate_id": None,
                "url": "",
                "created": "2025-03-24T14:36:29.647808Z",
            },
        },
        {
            "raw_event": {
                "serviceSource": "azureAdIdentityProtection",
                "detectorId": "detectorIdtest",
                "title": "titletest",
                "id": "id1",
                "providerAlertId": "providerAlertId",
                "alertWebUrl": "https://test.com",
                "description": "test",
                "detectionSource": "test",
                "productName": "test",
                "analyticId": "test",
                "createdDateTime": "2025-03-24T14:36:29.647818Z",
                "lastUpdateDateTime": "2025-03-24T14:36:29.647818Z",
                "firstActivityDateTime": "2025-03-24T14:36:29.647818Z",
                "lastActivityDateTime": "2025-03-24T14:36:29.647818Z",
                "systemTags": ["TEST"],
                "incidentWebUrl": "https://test.com",
                "incidentId": "test",
                "tenantId": "test",
                "assignedTo": "test",
                "status": "test",
                "severity": "INFO",
                "classification": "test",
                "evidence": [
                    {
                        "@odata.type": "#microsoft.graph.security.userEvidence",
                        "userAccount": {
                            "accountName": "test",
                            "domainName": "test",
                            "userSid": "test",
                            "azureAdUserId": "test",
                            "userPrincipalName": "test",
                            "displayName": "test",
                        },
                        "requestId": "test",
                    },
                    {
                        "@odata.type": "#microsoft.graph.security.cloudLogonSessionEvidence",
                        "sessionId": "test",
                        "startUtcDateTime": "2025-03-24T14:36:29.647818Z",
                    },
                    {
                        "@odata.type": "#microsoft.graph.security.ipEvidence",
                        "ipAddress": "test",
                        "countryLetterCode": "test",
                        "location": {
                            "state": "test",
                            "city": "test",
                            "longitude": -122.12094116210938,
                            "latitude": 47.68050003051758,
                        },
                    },
                ],
            },
            "event_timestamp": "2025-03-24T14:36:29.647818Z",
            "ioc": {
                "external_id": "TEST",
                "external_name": "test alert",
                "has_ioc_definition": True,
                "mitre_techniques": ["foo"],
            },
            "vendor_item_ref": {
                "id": "789",
                "title": "test IDP alert",
                "alternate_id": None,
                "url": "",
                "created": "2025-03-24T14:36:29.647808Z",
            },
        },
        {
            "raw_event": {
                "serviceSource": "microsoftDefenderForCloudApps",
            },
            "event_timestamp": "2025-03-24T14:36:29.647818Z",
            "ioc": {
                "external_id": "TEST",
                "external_name": "test alert",
                "has_ioc_definition": True,
                "mitre_techniques": ["foo"],
            },
            "vendor_item_ref": {
                "id": "789",
                "title": "test MDCA alert",
                "alternate_id": None,
                "url": "",
                "created": "2025-03-24T14:36:29.647808Z",
            },
        },
        {
            "raw_event": {
                "serviceSource": "microsoft365Defender",
            },
            "event_timestamp": "2025-03-24T14:36:29.647818Z",
            "ioc": {
                "external_id": "TEST",
                "external_name": "test alert",
                "has_ioc_definition": True,
                "mitre_techniques": ["foo"],
            },
            "vendor_item_ref": {
                "id": "789",
                "title": "test xdr alert",
                "alternate_id": None,
                "url": "",
                "created": "2025-03-24T14:36:29.647808Z",
            },
        },
        {
            "raw_event": {
                "serviceSource": "unimplemented",
            },
            "event_timestamp": "2025-03-24T14:36:29.647818Z",
            "ioc": {
                "external_id": "TEST",
                "external_name": "test alert",
                "has_ioc_definition": True,
                "mitre_techniques": ["foo"],
            },
            "vendor_item_ref": {
                "id": "789",
                "title": "test unimplemented alert",
                "alternate_id": None,
                "url": "",
                "created": "2025-03-24T14:36:29.647808Z",
            },
        },
    ]
    return artifact


critical_checks = [
    HealthCheckRequirement(
        name="Connection is valid",
        description="Can connect to the integration API",
        value=None,
        required=RequirementStatus.REQUIRED,
        status=ValidationStatus.PASSED,
    ),
]

dependency_checks = [
    HealthCheckRequirement(
        name="Required action",
        description="event_sync for ms_xdr must be enabled and healthy",
        value=None,
        required=RequirementStatus.REQUIRED,
        status=ValidationStatus.PASSED,
    ),
]

read_security_alerts = [
    HealthCheckRequirement(
        name="Read Security Alerts",
        description="Read Alerts in Microsoft Graph Security API",
        value="Read Security Alerts",
        required=RequirementStatus.REQUIRED,
        status=ValidationStatus.PASSED,
    ),
]

read_write_security_alerts = [
    HealthCheckRequirement(
        name="Read and Write Security Alerts",
        description="Read and Write Alerts in Microsoft Graph Security API",
        value="Read and Write Security Alerts",
        required=RequirementStatus.REQUIRED,
        status=ValidationStatus.PASSED,
    ),
]

read_write_custom_detections = [
    HealthCheckRequirement(
        name="Read and Write Custom Detections",
        description="Read and Write Custom Detections in Microsoft Graph Security API",
        value="Read and Write Custom Detections",
        required=RequirementStatus.REQUIRED,
        status=ValidationStatus.PASSED,
    ),
]

read_threat_hunting = [
    HealthCheckRequirement(
        name="Read Threat Hunting",
        description="Allows execution of Threat Hunting queries in Microsoft Graph Security API",
        value="Read Threat Hunting",
        required=RequirementStatus.REQUIRED,
        status=ValidationStatus.PASSED,
    ),
]


class MsXdrV1ApiTest(BaseTestCase):
    def setUp(self) -> None:
        super().setUp()

        self._patch_encryption()
        aad_app = AadAppFactory()
        integration = ConnectorFactory.get_integration(
            technology_id="ms_xdr", config__client_id=aad_app.client_id
        )
        setup_get_alerts_response(integration.config["tenant_id"])
        self.api = integration.get_api()

    @responses.activate
    def test_get_alerts(self):
        events = list(self.api.get_alerts()["value"])
        self.assertEqual(len(events), 1)
        self.assertEqual(events[0]["serviceSource"], "microsoftDefenderForEndpoint")


class MsXdrV1IntegrationTest(BaseIntegrationTest):
    def setUp(self) -> None:
        super().setUp()

    def setup_integration(self, settings=None):
        if settings is None:
            settings = self.settings

        aad_app = AadAppFactory()
        self.integration = ConnectorFactory.get_integration(
            technology_id="ms_xdr",
            config__client_id=aad_app.client_id,
            settings=settings,
        )

        self.tenant_id = self.integration.config["tenant_id"]

    @patch("apps.connectors.services.artifact_service.ArtifactService.read_lines")
    def test_event_sync_from_artifact(self, m_read_lines):
        lines = get_ms_xdr_events_artifact_lines()
        m_read_lines.return_value = lines
        self.setup_integration()

        response = self.integration.invoke_action(
            IntegrationActionType.EVENT_SYNC_FROM_ARTIFACT,
            **{
                "artifact_id": "_",
            },
        )

        result = serialize(list(response))
        self.assertEqual(len(result), 2)
        self.assertEqual(result[0]["vendor_item_ref"]["title"], "test xdr alert")
        self.assertEqual(
            result[1]["vendor_item_ref"]["title"], "test unimplemented alert"
        )

    @responses.activate
    def test_bookmarks(self):
        self.setup_integration()

        bookmark = self.integration.get_bookmark(IntegrationActionType.EVENT_SYNC)
        self.assertIsNotNone(bookmark)

        schema = MsXdrV1Bookmarks.model_json_schema()
        self.assertIn(IntegrationActionType.EVENT_SYNC.value, schema["properties"])

        schema = schema["properties"][IntegrationActionType.EVENT_SYNC.value]
        self.assertIn("latest_alert_update_datetime", schema["properties"])
        self.assertIn("latest_incident_update_datetime", schema["properties"])

    @responses.activate
    def test_event_sync(self):
        self.setup_integration()
        setup_get_alerts_response(self.tenant_id)
        setup_get_incidents_response(self.tenant_id)

        bookmark = self.integration.get_bookmark(IntegrationActionType.EVENT_SYNC)
        self.assertIsNotNone(bookmark)

        response = self.integration.invoke_action(
            IntegrationActionType.EVENT_SYNC,
            **{
                "bookmark": bookmark,
            },
        )

        result = serialize(list(response))
        alerts = [
            r
            for r in result
            if r["raw_event"]["@odata.type"] == "#microsoft.graph.security.alert"
        ]
        incidents = [
            r
            for r in result
            if r["raw_event"]["@odata.type"] == "#microsoft.graph.security.incident"
        ]
        self.assertEqual(len(alerts), 1)
        self.assertEqual(len(incidents), 1)
        self.assertEqual(
            alerts[0]["raw_event"]["serviceSource"], "microsoftDefenderForEndpoint"
        )

    @responses.activate
    def test_event_sync_no_incident(self):
        """
        Even if the incident hasn't been modified, certain parent information
        should still be populated on the alert.
        """
        self.setup_integration()
        setup_get_alerts_response(self.tenant_id)

        responses.get(
            f"https://graph.microsoft.com/v1.0/security/incidents",
            json={"value": []},
        )

        response = self.integration.invoke_action(IntegrationActionType.EVENT_SYNC)

        result = serialize(list(response))
        alerts = [
            r
            for r in result
            if r["raw_event"]["@odata.type"] == "#microsoft.graph.security.alert"
        ]
        incidents = [
            r
            for r in result
            if r["raw_event"]["@odata.type"] == "#microsoft.graph.security.incident"
        ]
        self.assertEqual(len(alerts), 1)
        self.assertEqual(len(incidents), 0)
        self.assertIsNotNone(alerts[0]["source_integration_id"])
        self.assertIsNotNone(alerts[0]["vendor_group_ref"]["url"])
        self.assertEqual(len(alerts[0]["vendor_parent_refs"]), 1)

    @responses.activate
    def test_event_sync_circular(self):
        self.setup_integration()

        setup_oauth_responses(self.tenant_id)
        setup_get_incidents_response(self.tenant_id)

        responses.get(
            f"https://graph.microsoft.com/v1.0/security/alerts_v2",
            json={
                "value": [
                    {
                        "@odata.type": "#microsoft.graph.security.alert",
                        "@odata.context": "https://graph.microsoft.com/beta/$metadata#security/alerts_v2/$entity",
                        "id": "fake",
                        "providerAlertId": "fake",
                        "incidentId": "123",
                        "status": "new",
                        "severity": "low",
                        "serviceSource": "microsoftDefenderForEndpoint",
                        "detectionSource": "customDetection",
                        "productName": "Microsoft Defender for Endpoint",
                        "detectorId": "fake",
                        "tenantId": "fake",
                        "title": "TEST - High Volume alert",
                        "description": "TEST - High Volume alert",
                        "recommendedActions": "",
                        "category": "InitialAccess",
                        "alertWebUrl": "https://security.microsoft.com/alerts/fake",
                        "incidentWebUrl": "https://security.microsoft.com/incident2/fake/overview",
                        "createdDateTime": "2025-05-20T10:49:40.18Z",
                        "lastUpdateDateTime": "2025-05-22T11:51:47.75Z",
                        "firstActivityDateTime": "2025-05-20T09:55:01.534981Z",
                        "lastActivityDateTime": "2025-05-22T11:45:01.942066Z",
                        "mitreTechniques": [],
                        "evidence": [
                            {
                                "@odata.type": "#microsoft.graph.security.processEvidence",
                                "createdDateTime": "2025-05-20T10:49:49.6Z",
                                "verdict": "suspicious",
                                "remediationStatus": "none",
                                "processId": 2999989,
                                "parentProcessId": 2999989,
                                "processCommandLine": "/usr/lib/systemd/systemd --user",
                                "processCreationDateTime": "2025-05-20T09:56:43.299779Z",
                                "parentProcessCreationDateTime": "2025-05-20T09:56:43.29Z",
                                "imageFile": {
                                    "sha1": "62380307bd205ed03d6f0aa497cee8577d32efb7",
                                    "sha256": "757be52bdf1cc709a81771c05de81441d77f77fe208a864d8f02789d8ad35a50",
                                    "fileName": "systemd",
                                    "filePath": "/usr/lib/systemd",
                                },
                                "userAccount": {
                                    "accountName": "root",
                                    "userSid": "",
                                    "displayName": "",
                                },
                            },
                        ],
                    }
                ]
            },
        )

        response = self.integration.invoke_action(IntegrationActionType.EVENT_SYNC)

        result = serialize(list(response))
        alerts = [
            r
            for r in result
            if r["raw_event"]["@odata.type"] == "#microsoft.graph.security.alert"
        ]

        self.assertEqual(len(alerts), 1)
        self.assertIsNone(
            alerts[0]["ocsf"]["evidences"][0]["process"]["parent_process"]
        )

    def test_normalize_iot_evidence(self):
        iot_evidence = {
            "@odata.type": "#microsoft.graph.security.ioTDeviceEvidence",
            "createdDateTime": "String (timestamp)",
            "verdict": "String",
            "remediationStatus": "String",
            "remediationStatusDetails": "String",
            "roles": ["String"],
            "tags": ["String"],
            "iotHub": {"@odata.type": "microsoft.graph.security.azureResourceEvidence"},
            "deviceId": "String",
            "deviceName": "String",
            "owners": ["String"],
            "iotSecuritySolutionId": "Guid",
            "ioTSecurityAgentId": "String",
            "deviceType": "String",
            "deviceTypeId": "String",
            "source": "String",
            "sourceRef": {"url": "https://link.com"},
            "manufacturer": "String",
            "model": "String",
            "operatingSystem": "String",
            "ipAddress": {"ipAddress": "*******"},
            "nics": [
                {
                    "macAddress": "00:00:00:00",
                    "ipAddress": {"ipAddress": "*******"},
                }
            ],
            "protocols": ["String"],
            "serialNumber": "String",
            "site": "String",
            "zone": "String",
            "sensor": "String",
            "purdueLayer": "String",
            "isProgramming": True,
            "isAuthorized": True,
            "isScanner": True,
            "devicePageLink": "String",
            "deviceSubType": "String",
        }

        evidence = _normalize_iot_device(iot_evidence)
        result = evidence.model_dump(
            mode="json", exclude_none=True, exclude_defaults=True
        )
        expected = {
            "device": {
                "agent_list": [{"uid": "String"}],
                "desc": "String",
                "hw_info": {"serial_number": "String", "vendor_name": "String"},
                "ip": "*******",
                "is_trusted": True,
                "location": {"desc": "String - String"},
                "model": "String",
                "name": "String",
                "network_interfaces": [{"ip": "*******", "mac": "00:00:00:00"}],
                "os": {"name": "String"},
                "owner": {"name": "String"},
                "type": "(7, 'IoT')",
                "uid": "String",
                "vendor_name": "String",
            },
            "url": {
                "url_string": "https://link.com",
                "scheme": "https",
                "netloc": "link.com",
                "hostname": "link.com",
            },
            "verdict": "Other",
            "verdict_id": 99,
        }
        self.assertEqual(result, expected)

    @responses.activate
    def test_update_lifecycle_status(self):
        self.setup_integration()
        setup_update_alert_response(self.tenant_id, "123")

        self.integration.invoke_action(
            IntegrationActionType.UPDATE_LIFECYCLE_STATUS,
            **{
                "vendor_sync_id": "123",
                "status": "new",
                "verdict": "true_positive",
                "assigned_to": "test user",
                "comment": "test comment",
            },
        )

    @responses.activate
    def test_add_alert_comment(self):
        self.setup_integration()
        setup_add_alert_comment_response(self.tenant_id, "123")

        self.integration.invoke_action(
            IntegrationActionType.ADD_ALERT_COMMENT,
            **{
                "vendor_sync_id": "123",
                "comment": "test comment",
            },
        )

    @responses.activate
    def test_list_iocs(self):
        self.setup_integration()
        setup_detection_rule_responses(self.tenant_id)

        response = self.integration.invoke_action(
            IntegrationActionType.LIST_IOCS,
            **{"source_ids": ["7506", "8575", "9794"]},
        )

        result = serialize(list(response))
        self.assertEqual(len(result), 3)

    @responses.activate
    def test_save_ioc(self):
        self.setup_integration()
        setup_detection_rule_responses(self.tenant_id)

        # create
        self.integration.invoke_action(
            IntegrationActionType.SAVE_IOC,
            **{
                "source_data": {},
                "title": "test rule",
                "description": "test rule",
                "query": "test query",
                "status": "active",
            },
        )

        # update
        self.integration.invoke_action(
            IntegrationActionType.SAVE_IOC,
            **{
                "source_id": "67895317-b2a8-4ac3-8f8b-fa6b7765f2fe",
                "source_data": {},
                "title": "test rule",
                "description": "test rule",
                "query": "test query",
                "status": "active",
            },
        )

    @responses.activate
    def test_delete_ioc(self):
        self.setup_integration()
        setup_detection_rule_responses(self.tenant_id)

        self.integration.invoke_action(
            IntegrationActionType.DELETE_IOC,
            **{
                "source_id": "67895317-b2a8-4ac3-8f8b-fa6b7765f2fe",
            },
        )

    @responses.activate
    def test_list_data_sources(self):
        self.setup_integration()
        setup_data_sources_responses(self.tenant_id)

        response = self.integration.invoke_action(
            IntegrationActionType.LIST_DATA_SOURCES,
            **{
                "start_time": "2024-03-26Z",
                "end_time": "2024-04-26Z",
            },
        )

        result = serialize(list(response))
        self.assertEqual(len(result), 2)
        self.assertEqual(result[0]["source_name"], "SecurityAlert")
        self.assertEqual(result[1]["source_name"], "SecurityIncident")

    def test_normalize_amazon_resource(self):
        evidence = {
            "amazonResourceId": "arn:aws:ec2:region:account:instance/instance-id",
            "resourceType": "AWS::EC2::Instance",
            "resourceName": "TestInstance",
            "verdict": "suspicious",
        }
        from apps.connectors.integrations.vendors.microsoft.ms_xdr.v1.actions.event_sync import (
            _normalize_amazon_resource,
        )

        result = _normalize_amazon_resource(evidence).model_dump(
            mode="json", exclude_none=True, exclude_defaults=True
        )
        expected = {
            "resources": [
                {
                    "uid": "arn:aws:ec2:region:account:instance/instance-id",
                    "type": "AWS::EC2::Instance",
                    "name": "TestInstance",
                }
            ],
            "verdict": "Suspicious",
            "verdict_id": 4,
        }
        self.assertEqual(result, expected)

    def test_normalize_google_resource(self):
        evidence = {
            "resourceType": "GCP::Compute::Instance",
            "resourceName": "gcp-instance",
            "verdict": "malicious",
        }
        from apps.connectors.integrations.vendors.microsoft.ms_xdr.v1.actions.event_sync import (
            _normalize_google_resource,
        )

        result = _normalize_google_resource(evidence).model_dump(
            mode="json", exclude_none=True, exclude_defaults=True
        )
        expected = {
            "resources": [{"type": "GCP::Compute::Instance", "name": "gcp-instance"}],
            "verdict": "Security Risk",
            "verdict_id": 8,
        }
        self.assertEqual(result, expected)

    def test_normalize_blob_container_evidence(self):
        evidence = {
            "name": "container1",
            "storageResource": {
                "resourceType": "Azure::Storage::Container",
                "resourceName": "container1",
                "resourceId": "container1-id",
            },
            "verdict": "unknown",
        }
        from apps.connectors.integrations.vendors.microsoft.ms_xdr.v1.actions.event_sync import (
            _normalize_blob_container_evidence,
        )

        result = _normalize_blob_container_evidence(evidence).model_dump(
            mode="json", exclude_none=True, exclude_defaults=True
        )
        expected = {
            "resources": [
                {
                    "type": "Azure::Storage::Container",
                    "name": "container1",
                    "uid": "container1-id",
                }
            ],
            "databucket": {"name": "container1"},
            "verdict": "Unknown",
            "verdict_id": 0,
        }
        self.assertEqual(result, expected)

    def test_normalize_blob_evidence(self):
        evidence = {
            "name": "blob1.txt",
            "fileHashes": [{"value": "abc123", "algorithm": "sha256"}],
            "blobContainer": {
                "name": "container1",
                "storageResource": {
                    "resourceType": "Azure::Storage::Container",
                    "resourceName": "container1",
                    "resourceId": "container1-id",
                },
            },
            "verdict": "malicious",
        }
        from apps.connectors.integrations.vendors.microsoft.ms_xdr.v1.actions.event_sync import (
            _normalize_blob_evidence,
        )

        result = _normalize_blob_evidence(evidence).model_dump(
            mode="json", exclude_none=True, exclude_defaults=True
        )
        expected = {
            "resources": [
                {
                    "type": "Azure::Storage::Container",
                    "name": "container1",
                    "uid": "container1-id",
                }
            ],
            "databucket": {
                "name": "container1",
                "file": {
                    "name": "blob1.txt",
                    "hashes": [
                        {"value": "abc123", "algorithm": "SHA-256", "algorithm_id": 3}
                    ],
                },
            },
            "verdict": "Security Risk",
            "verdict_id": 8,
        }
        self.assertEqual(result, expected)

    def test_normalize_security_group_evidence(self):
        evidence = {
            "securityGroupId": "sg-12345",
            "displayName": "Test Security Group",
            "verdict": "noThreatsFound",
        }
        from apps.connectors.integrations.vendors.microsoft.ms_xdr.v1.actions.event_sync import (
            _normalize_security_group_evidence,
        )

        result = _normalize_security_group_evidence(evidence).model_dump(
            mode="json", exclude_none=True, exclude_defaults=True
        )
        expected = {
            "resources": [
                {"group": {"uid": "sg-12345", "name": "Test Security Group"}}
            ],
            "verdict": "False Positive",
            "verdict_id": 1,
        }
        self.assertEqual(result, expected)

    def test_normalize_azure_resource_evidence(self):
        evidence = {
            "resourceId": "subscriptions/123/resourceGroups/myResourceGroup/providers/Microsoft.Compute/virtualMachines/myVM",
            "resourceType": "Microsoft.Compute/virtualMachines",
            "resourceName": "myVM",
            "verdict": "suspicious",
        }
        from apps.connectors.integrations.vendors.microsoft.ms_xdr.v1.actions.event_sync import (
            _normalize_azure_resource_evidence,
        )

        result = _normalize_azure_resource_evidence(evidence).model_dump(
            mode="json", exclude_none=True, exclude_defaults=True
        )
        expected = {
            "resources": [
                {
                    "uid": "subscriptions/123/resourceGroups/myResourceGroup/providers/Microsoft.Compute/virtualMachines/myVM",
                    "type": "Microsoft.Compute/virtualMachines",
                    "name": "myVM",
                }
            ],
            "verdict": "Suspicious",
            "verdict_id": 4,
        }
        self.assertEqual(result, expected)

    def test_normalize_malware_evidence(self):
        evidence = {
            "files": [
                {"fileDetails": {"fileName": "malware.exe"}, "verdict": "malicious"},
                {"fileDetails": {"fileName": "malware2.exe"}, "verdict": "malicious"},
            ],
            "processes": [
                {
                    "processId": 123,
                    "processCommandLine": "run malware.exe",
                    "verdict": "malicious",
                }
            ],
        }
        from apps.connectors.integrations.vendors.microsoft.ms_xdr.v1.actions.event_sync import (
            _normalize_malware_evidence,
        )

        result = [
            _e.model_dump(mode="json", exclude_none=True, exclude_defaults=True)
            for _e in _normalize_malware_evidence(evidence)
        ]
        self.assertEqual(len(result), 3)
        self.assertEqual(result[0]["file"]["name"], "malware.exe")
        self.assertEqual(result[0]["verdict"], "Security Risk")
        self.assertEqual(result[2]["process"]["pid"], 123)
        self.assertEqual(result[2]["process"]["cmd_line"], "run malware.exe")
        self.assertEqual(result[2]["verdict"], "Security Risk")


class MsXdrV1HealthCheckTest(BaseTestCase):
    def setUp(self) -> None:
        super().setUp()

        self._patch_encryption()
        aad_app = AadAppFactory()
        self.connector = ConnectorFactory(
            technology_id="ms_xdr",
            config__client_id=aad_app.client_id,
        )
        self.integration = self.connector.get_integration(decrypt_config=False)
        self.tenant_id = self.integration.config["tenant_id"]

    @responses.activate
    def test_connection_valid(self):
        setup_get_alerts_response(self.tenant_id)
        health_check = ConnectionHealthCheck(integration=self.integration)
        self.assertEqual(health_check.get_result(), IntegrationHealthCheckResult.PASSED)

    @responses.activate
    def test_connection_invalid(self):
        responses.post(
            f"https://login.microsoftonline.com/{self.tenant_id}/oauth2/v2.0/token",
            status=HTTPStatus.UNAUTHORIZED,
            json={
                "error": "invalid_credentials",
                "error_description": "Invalid credentials}",
            },
        )

        health_check = ConnectionHealthCheck(integration=self.integration)
        self.assertEqual(health_check.get_result(), IntegrationHealthCheckResult.FAILED)

    @responses.activate
    def test_read_alerts_failed(self):
        responses.post(
            f"https://login.microsoftonline.com/{self.tenant_id}/oauth2/v2.0/token",
            status=HTTPStatus.UNAUTHORIZED,
            json={
                "error": "invalid_credentials",
                "error_description": "Invalid credentials}",
            },
        )

        health_check = ReadAlerts(self.integration)
        self.assertEqual(health_check.get_result(), IntegrationHealthCheckResult.FAILED)


class MsXdrV1HealthCheckComponentsTest(BaseTestCase, HealthCheckComponentTestMixin):
    def setUp(self) -> None:
        super().setUp()
        self._patch_encryption()

        aad_app = AadAppFactory()
        self.connector = ConnectorFactory(
            technology_id="ms_xdr",
            config__client_id=aad_app.client_id,
            enabled_actions=[
                "event_sync",
                "update_lifecycle_status",
                "delete_ioc",
                "list_data_sources",
                "list_iocs",
                "save_ioc",
                "add_alert_comment",
            ],
        )
        self.integration = self.connector.get_integration(decrypt_config=False)
        self.tenant_id = self.integration.config["tenant_id"]

    @responses.activate
    def test_components(self):
        setup_oauth_responses(self.tenant_id)

        components = HealthCheckComponent.get_components(connector=self.connector)

        self.assert_components(
            components,
            [
                critical_checks,
                read_security_alerts,
                read_write_security_alerts,
                read_write_security_alerts,
                read_write_custom_detections,
                read_write_custom_detections,
                read_write_custom_detections,
                read_threat_hunting,
            ],
        )
