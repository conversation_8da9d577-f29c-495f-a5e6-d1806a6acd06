
import responses

from apps.connectors.integrations import IntegrationActionType

from apps.connectors.tests.integrations.base import (
    BaseIntegrationTest,
    HealthCheckComponentTestMixin,
)
from apps.tests.base import BaseTestCase
from factories import ConnectorFactory

class NozomiVantageV1ApiTest(BaseTestCase):
    pass

class NozomiVantageV1IntegrationTest(BaseIntegrationTest):
    pass

class NozomiVantageV1HealthCheckTest(BaseTestCase):
    pass

class NozomiVantageV1HealthCheckComponentsTest(BaseTestCase, HealthCheckComponentTestMixin):
    pass
