from unittest.mock import patch

import responses

from apps.connectors.health_checks.components.component import (
    HealthCheckComponent,
    HealthCheckRequirement,
    RequirementStatus,
    ValidationStatus,
)
from apps.connectors.integrations.actions.action import IntegrationActionType
from apps.connectors.integrations.actions.event_sync import EventSyncFromArtifact
from apps.connectors.integrations.actions.host import (
    HostIsolationResult,
    IsolateHostAction,
    UnisolateHostAction,
)
from apps.connectors.integrations.health_check import IntegrationHealthCheckResult
from apps.connectors.integrations.schemas.action_polling_context import (
    IntegrationActionPollingContext,
)
from apps.connectors.integrations.vendors.microsoft.ms_mde.v1.actions import (
    MsMdeV1InternalActions,
)
from apps.connectors.integrations.vendors.microsoft.ms_xdr.v1.health_check import (
    ConnectionHealthCheck,
)
from apps.connectors.tests.integrations.base import (
    BaseIntegrationTest,
    HealthCheckComponentTestMixin,
)
from apps.connectors.tests.integrations.ms_xdr_v1 import (
    critical_checks,
    dependency_checks,
    get_ms_xdr_events_artifact_lines,
    setup_oauth_responses,
)
from apps.connectors.utils import serialize
from apps.tests.base import BaseTestCase
from factories import ConnectorFactory
from factories.aad_app import AadAppFactory


class MsMdeV1ApiTest(BaseTestCase):
    pass


class MsMdeV1IntegrationTest(BaseIntegrationTest):
    def setUp(self) -> None:
        super().setUp()

    def setup_integration(self, settings=None):
        if settings is None:
            settings = self.settings

        aad_app = AadAppFactory()
        self.integration = ConnectorFactory.get_integration(
            technology_id="ms_mde",
            config__client_id=aad_app.client_id,
            settings=settings,
        )

        self.tenant_id = self.integration.config["tenant_id"]

    @patch("apps.connectors.services.artifact_service.ArtifactService.read_lines")
    def test_event_sync_from_artifact(self, m_read_lines):
        lines = get_ms_xdr_events_artifact_lines()
        m_read_lines.return_value = lines
        self.setup_integration()

        response = self.integration.invoke_action(
            IntegrationActionType.EVENT_SYNC_FROM_ARTIFACT,
            **{
                "artifact_id": "_",
            },
        )

        result = serialize(list(response))
        self.assertEqual(len(result), 1)
        self.assertEqual(result[0]["vendor_item_ref"]["title"], "test MDE alert")

    @responses.activate
    def test_isolate_host(self):
        self.setup_integration()
        setup_oauth_responses(self.tenant_id)

        responses.get(
            "https://api.securitycenter.windows.com/api/v1.0/machineactions",
            json={"value": []},
        )
        responses.post(
            "https://api.securitycenter.windows.com/api/v1.0/machines/test_source_id/isolate",
            json={"id": 1, "status": "Succeeded"},
        )
        responses.post(
            "https://api.securitycenter.windows.com/api/v1.0/machines/test_source_id/unisolate",
            json={"id": 1, "status": "Succeeded"},
        )

        result = self.integration.invoke_action(
            IntegrationActionType.ISOLATE_HOST,
            **{
                "host": {
                    "value": "test_source_id",
                    "value_type": "host_identifier",
                },
                "console_comment": "test comment",
            },
        )

        self.assertIsInstance(result, HostIsolationResult)
        self.assertEqual(result.error, None)

        result = self.integration.invoke_action(
            MsMdeV1InternalActions.SELECTIVE_HOST_ISOLATE,
            **{
                "host": {
                    "value": "test_source_id",
                    "value_type": "host_identifier",
                },
                "console_comment": "test comment",
            },
        )

        self.assertIsInstance(result, HostIsolationResult)
        self.assertEqual(result.error, None)

        result = self.integration.invoke_action(
            IntegrationActionType.UNISOLATE_HOST,
            **{
                "host": {
                    "value": "test_source_id",
                    "value_type": "host_identifier",
                },
                "console_comment": "test comment",
            },
        )

        self.assertIsInstance(result, HostIsolationResult)
        self.assertEqual(result.error, None)

    @responses.activate
    def test_isolate_host_already_isolated(self):
        self.setup_integration()
        setup_oauth_responses(self.tenant_id)

        responses.get(
            "https://api.securitycenter.windows.com/api/v1.0/machineactions",
            json={"value": [{"id": 1, "type": "Isolate", "status": "Succeeded"}]},
        )

        result = self.integration.invoke_action(
            IntegrationActionType.ISOLATE_HOST,
            **{
                "host": {
                    "value": "test_source_id",
                    "value_type": "host_identifier",
                },
                "console_comment": "test comment",
            },
        )

        self.assertIsInstance(result, HostIsolationResult)
        self.assertEqual(
            result.error.message, "Machine test_source_id is already isolated."
        )

    @responses.activate
    def test_isolate_host_error(self):
        self.setup_integration()
        setup_oauth_responses(self.tenant_id)

        responses.get(
            "https://api.securitycenter.windows.com/api/v1.0/machineactions",
            json={"value": []},
        )
        responses.post(
            "https://api.securitycenter.windows.com/api/v1.0/machines/test_source_id/isolate",
            json={"id": 1, "status": "UNRECOGNIZED_MADE_UP_STATUS"},
        )

        result = self.integration.invoke_action(
            IntegrationActionType.ISOLATE_HOST,
            **{
                "host": {
                    "value": "test_source_id",
                    "value_type": "host_identifier",
                },
                "console_comment": "test comment",
            },
        )
        self.assertIsInstance(result, HostIsolationResult)
        self.assertIsNotNone(result.error)

    @responses.activate
    def test_isolate_host_polling(self):
        self.setup_integration()
        setup_oauth_responses(self.tenant_id)

        responses.get(
            "https://api.securitycenter.windows.com/api/v1.0/machineactions",
            json={"value": []},
        )
        responses.post(
            "https://api.securitycenter.windows.com/api/v1.0/machines/test_source_id/isolate",
            json={"id": 1, "status": "Pending"},
        )

        result = self.integration.invoke_action(
            IntegrationActionType.ISOLATE_HOST,
            **{
                "host": {
                    "value": "test_source_id",
                    "value_type": "host_identifier",
                },
                "console_comment": "test comment",
            },
        )

        self.assertIsInstance(result, IntegrationActionPollingContext)
        context = result.context
        self.assertEqual(context["machine_action"]["status"], "Pending")
        self.assertEqual(context["machine_action"]["id"], 1)

        responses.get(
            "https://api.securitycenter.windows.com/api/v1.0/machineactions/1",
            json={"id": 1, "status": "Pending"},
        )

        result = self.integration.poll_action(
            IntegrationActionType.ISOLATE_HOST,
            poll_context=IntegrationActionPollingContext(
                context=context,
            ),
        )

        self.assertIsInstance(result, IntegrationActionPollingContext)
        self.assertEqual(result.context["machine_action"]["status"], "Pending")
        self.assertEqual(result.context["machine_action"]["id"], 1)

        result = self.integration.poll_action(
            MsMdeV1InternalActions.SELECTIVE_HOST_ISOLATE,
            poll_context=IntegrationActionPollingContext(
                context=context,
            ),
        )

        self.assertIsInstance(result, IntegrationActionPollingContext)
        self.assertEqual(result.context["machine_action"]["status"], "Pending")
        self.assertEqual(result.context["machine_action"]["id"], 1)

        result = self.integration.poll_action(
            IntegrationActionType.UNISOLATE_HOST,
            poll_context=IntegrationActionPollingContext(
                context=context,
            ),
        )

        self.assertIsInstance(result, IntegrationActionPollingContext)
        self.assertEqual(result.context["machine_action"]["status"], "Pending")
        self.assertEqual(result.context["machine_action"]["id"], 1)


class MsMdeV1HealthCheckTest(BaseTestCase):
    def setUp(self) -> None:
        super().setUp()

        self._patch_encryption()
        aad_app = AadAppFactory()
        self.connector = ConnectorFactory(
            technology_id="ms_mde",
            config__client_id=aad_app.client_id,
        )
        self.integration = self.connector.get_integration(decrypt_config=False)
        self.tenant_id = self.integration.config["tenant_id"]

    @responses.activate
    def test_connection_valid(self):
        setup_oauth_responses(self.tenant_id)
        health_check = ConnectionHealthCheck(integration=self.integration)
        self.assertEqual(health_check.get_result(), IntegrationHealthCheckResult.PASSED)


class MsMdeV1HealthCheckComponentsTest(BaseTestCase, HealthCheckComponentTestMixin):
    def setUp(self) -> None:
        super().setUp()
        self._patch_encryption()

        aad_app = AadAppFactory()
        self.connector = ConnectorFactory(
            technology_id="ms_mde",
            config__client_id=aad_app.client_id,
            enabled_actions=[
                EventSyncFromArtifact.action_type.value,
                IsolateHostAction.action_type.value,
                UnisolateHostAction.action_type.value,
                MsMdeV1InternalActions.SELECTIVE_HOST_ISOLATE.value,
            ],
        )
        self.integration = self.connector.get_integration(decrypt_config=False)
        self.tenant_id = self.integration.config["tenant_id"]

        # Create a MS XDR connector for testing the dependency health check
        ConnectorFactory(
            organization=self.connector.organization,
            technology_id="ms_xdr",
            config__client_id=aad_app.client_id,
            config__tenant_id=self.tenant_id,
            enabled_actions=[
                "event_sync",
            ],
        )

    @responses.activate
    def test_components(self):
        setup_oauth_responses(self.tenant_id)
        components = HealthCheckComponent.get_components(connector=self.connector)

        isolate_machine_checks = [
            HealthCheckRequirement(
                name="Isolate and Unisolate Machines",
                description="Isolate and Unisolate Machines in Microsoft Defender for Endpoint API",
                value="Isolate and Unisolate Machines",
                required=RequirementStatus.REQUIRED,
                status=ValidationStatus.PASSED,
            ),
            HealthCheckRequirement(
                name="List Machine Actions",
                description="List Machine Actions in Microsoft Defender for Endpoint API",
                value="List Machine Actions",
                required=RequirementStatus.REQUIRED,
                status=ValidationStatus.PASSED,
            ),
        ]

        self.assert_components(
            components,
            [
                isolate_machine_checks,
                isolate_machine_checks,
                isolate_machine_checks,
                critical_checks,
                dependency_checks,
            ],
        )
