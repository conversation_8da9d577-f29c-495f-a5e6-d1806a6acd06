from datetime import datetime, timedelta, timezone
from unittest import mock

import responses

import apps.connectors.integrations.vendors.palo_alto.palo_alto_prisma_cloud.v1.actions.event_sync as event_sync
from apps.connectors.integrations.actions.action import IntegrationActionType
from apps.connectors.integrations.actions.update_lifecycle_status import (
    CorrIncidentStatus,
    UpdateLifecycleStatusResult,
)
from apps.connectors.integrations.health_check import IntegrationHealthCheckResult
from apps.connectors.integrations.schemas import ocsf
from apps.connectors.integrations.vendors.palo_alto.palo_alto_prisma_cloud.v1.bookmarks import (
    PaloAltoPrismaCloudEventSyncBookmark,
    PaloAltoPrismaCloudEventSyncBookmarks,
)
from apps.connectors.integrations.vendors.palo_alto.palo_alto_prisma_cloud.v1.health_check import (
    ConnectionHealthCheck,
)
from apps.connectors.tests.integrations.base import (
    BaseIntegrationTest,
)
from apps.connectors.tests.integrations.claroty_xdome_v1 import setup_basic_responses
from apps.connectors.utils import serialize
from apps.tests.base import BaseTestCase
from factories.connector import ConnectorFactory


def alert_response(id="alert1", page_token=None):
    response = {
        "items": [
            {
                "id": id,
            }
        ],
    }
    if page_token:
        response["nextPageToken"] = page_token
    return response


def alert_detail_response(alert_id):
    return {
        "alertAdditionalInfo": {},
        "alertAttribution": {
            "attributionEventList": [
                {"event": "string", "event_ts": 0, "username": "string"}
            ],
            "resourceCreatedBy": "string",
            "resourceCreatedOn": 0,
        },
        "alertCount": 0,
        "alertRules": [
            {
                "alertRuleNotificationConfig": [
                    {
                        "dayOfMonth": 0,
                        "daysOfWeek": [{"day": "SU", "offset": 0}],
                        "detailedReport": True,
                        "enabled": True,
                        "frequency": "as_it_happens",
                        "frequencyFromRRule": "string",
                        "hourOfDay": 0,
                        "id": "string",
                        "includeRemediation": True,
                        "lastUpdated": 0,
                        "last_sent_ts": 0,
                        "recipients": ["string"],
                        "rruleSchedule": "string",
                        "templateId": "string",
                        "timezone": "string",
                        "type": "email",
                        "withCompression": True,
                    }
                ],
                "allowAutoRemediate": True,
                "delayNotificationMs": 0,
                "description": "string",
                "enabled": True,
                "lastModifiedBy": "string",
                "lastModifiedOn": 0,
                "name": "string",
                "notifyOnDismissed": True,
                "notifyOnOpen": True,
                "notifyOnResolved": True,
                "notifyOnSnoozed": True,
                "policies": ["string"],
                "policyLabels": ["string"],
                "policyScanConfigId": "string",
                "scanAll": True,
                "target": {
                    "accountGroups": ["string"],
                    "alertRulePolicyFilter": {
                        "availablePolicyFilters": ["string"],
                        "cloud.type": ["ALL"],
                        "policy.complianceStandard": ["string"],
                        "policy.label": ["string"],
                        "policy.severity": ["string"],
                    },
                    "excludedAccounts": ["string"],
                    "includedResourceLists": {"computeAccessGroupIds": ["string"]},
                    "regions": ["string"],
                    "tags": [{"key": "string", "values": ["string"]}],
                },
            }
        ],
        "alertTime": "2026-10-01T00:00:00Z",
        "appMetadata": [{}],
        "connectionDetails": [
            {
                "accepted": "string",
                "accountName": "string",
                "classification": "string",
                "destIp": "string",
                "destIsp": "string",
                "feedSource": "string",
                "id": 1234,
                "inboundTrafficVolume": 0,
                "markedThreatTs": 0,
                "outboundTrafficVolume": 0,
                "packets": 0,
                "srcIp": "string",
                "srcIsp": "string",
                "threatDescription": "string",
                "timestamp": 0,
                "trafficOverTime": [{}],
                "trafficVolume": 0,
            }
        ],
        "dismissalDuration": "string",
        "dismissalNote": "string",
        "dismissalUntilTs": 0,
        "dismissedBy": "string",
        "eventOccurred": 0,
        "firstSeen": "2023-10-01T00:00:00Z",
        "history": [{"reason": "string", "status": "OPEN"}],
        "id": alert_id,
        "investigateOptions": {
            "alertId": "string",
            "hasSearchExecutionSupport": True,
            "searchId": "string",
        },
        "lastSeen": "2023-10-01T00:00:00Z",
        "lastUpdated": "2023-10-01T00:00:00Z",
        "metadata": {},
        "policy": {
            "cloudType": "ALL",
            "complianceMetadata": [
                {
                    "complianceId": "string",
                    "customAssigned": True,
                    "policyId": "string",
                    "requirementDescription": "string",
                    "requirementId": "string",
                    "requirementName": "string",
                    "sectionDescription": "string",
                    "sectionId": "string",
                    "sectionLabel": "string",
                    "standardDescription": "string",
                    "standardId": "string",
                    "standardName": "string",
                }
            ],
            "createdBy": "string",
            "createdOn": "2023-10-01T00:00:00Z",
            "deleted": True,
            "description": "string",
            "enabled": True,
            "findingTypes": ["string"],
            "labels": [{"string": "string"}],
            "lastModifiedBy": "string",
            "lastModifiedOn": "2023-10-01T00:00:00Z",
            "name": "string",
            "overridden": True,
            "policyId": "string",
            "policySubTypes": ["run"],
            "policyType": "config",
            "policyUpi": "string",
            "recommendation": "string",
            "remediable": True,
            "remediation": {
                "actions": [{"operation": "string", "payload": "string"}],
                "cliScriptTemplate": "string",
                "description": "string",
            },
            "restrictAlertDismissal": True,
            "rule": {
                "apiName": "string",
                "cloudAccount": "string",
                "cloudType": "string",
                "criteria": "string",
                "dataCriteria": {
                    "classificationResult": "string",
                    "exposure": "private",
                    "extension": ["string"],
                },
                "name": "string",
                "parameters": {},
                "resourceIdPath": "string",
                "resourceType": "string",
                "type": "Config",
            },
            "ruleLastModifiedOn": 0,
            "severity": "high",
            "systemDefault": True,
        },
        "policyId": "string",
        "reason": "string",
        "resource": {
            "account": "string",
            "accountId": "string",
            "additionalInfo": {
                "array": True,
                "bigDecimal": True,
                "bigInteger": True,
                "binary": True,
                "boolean": True,
                "containerNode": True,
                "double": True,
                "float": True,
                "floatingPointNumber": True,
                "int": True,
                "integralNumber": True,
                "long": True,
                "missingNode": True,
                "nodeType": "ARRAY",
                "null": True,
                "number": True,
                "object": True,
                "pojo": True,
                "short": True,
                "textual": True,
                "valueNode": True,
            },
            "cloudAccountAncestors": ["string"],
            "cloudAccountGroups": ["string"],
            "cloudAccountOwners": ["string"],
            "cloudServiceName": "string",
            "cloudType": "ALL",
            "data": {},
            "id": "string",
            "name": "string",
            "region": "string",
            "regionId": "string",
            "resourceApiName": "string",
            "resourceConfigJsonAvailable": True,
            "resourceDetailsAvailable": True,
            "resourceTags": {},
            "resourceType": "string",
            "rrn": "string",
            "unifiedAssetId": "string",
            "url": "string",
        },
        "riskDetail": {
            "policyScores": [
                {
                    "cloudType": "ALL",
                    "complianceMetadata": [
                        {
                            "complianceId": "string",
                            "customAssigned": True,
                            "policyId": "string",
                            "requirementDescription": "string",
                            "requirementId": "string",
                            "requirementName": "string",
                            "sectionDescription": "string",
                            "sectionId": "string",
                            "sectionLabel": "string",
                            "standardDescription": "string",
                            "standardId": "string",
                            "standardName": "string",
                        }
                    ],
                    "createdBy": "string",
                    "createdOn": 0,
                    "deleted": True,
                    "description": "string",
                    "enabled": True,
                    "findingTypes": ["string"],
                    "labels": ["string"],
                    "lastModifiedBy": "string",
                    "lastModifiedOn": 0,
                    "name": "string",
                    "overridden": True,
                    "points": "string",
                    "policyId": "string",
                    "policySubTypes": ["run"],
                    "policyType": "config",
                    "policyUpi": "string",
                    "recommendation": "string",
                    "remediable": True,
                    "remediation": {
                        "actions": [{"operation": "string", "payload": "string"}],
                        "cliScriptTemplate": "string",
                        "description": "string",
                    },
                    "restrictAlertDismissal": True,
                    "riskScore": {"maxScore": 0, "score": 0},
                    "rule": {
                        "apiName": "string",
                        "cloudAccount": "string",
                        "cloudType": "string",
                        "criteria": "string",
                        "dataCriteria": {
                            "classificationResult": "string",
                            "exposure": "private",
                            "extension": ["string"],
                        },
                        "name": "string",
                        "parameters": {},
                        "resourceIdPath": "string",
                        "resourceType": "string",
                        "type": "Config",
                    },
                    "ruleLastModifiedOn": 0,
                    "severity": "high",
                    "systemDefault": True,
                }
            ],
            "rating": "string",
            "riskScore": {"maxScore": 0, "score": 0},
            "score": "12",
        },
        "saveSearchId": "string",
        "status": "open",
        "triggeredBy": "string",
    }


def mock_response():
    responses.add(
        responses.POST,
        f"https://test_base_url.com/login",
        json={"token": "mocked_token"},
        status=200,
        content_type="application/json",
    )
    responses.add(
        responses.POST,
        f"https://test_base_url.com/alert/dismiss",
        json={},
        status=200,
        content_type="application/json",
    )
    responses.add(
        responses.POST,
        f"https://test_base_url.com/alert/reopen",
        json={},
        status=200,
        content_type="application/json",
    )
    responses.add(
        responses.GET,
        "https://test_base_url.com/v2/alert?timeType=relative&timeAmount=100&timeUnit=minute&detailed=true",
        json=alert_response("alert1", "token1"),
        status=200,
        content_type="application/json",
    )
    responses.add(
        responses.GET,
        "https://test_base_url.com/v2/alert?timeType=relative&timeAmount=100&timeUnit=minute&detailed=true&pageToken=token1",
        json=alert_response("alert2"),
        status=200,
        content_type="application/json",
    )
    responses.add(
        responses.GET,
        f"https://test_base_url.com/v2/alert/alert1",
        json=alert_detail_response("alert1"),
        status=200,
        content_type="application/json",
    )
    responses.add(
        responses.GET,
        f"https://test_base_url.com/v2/alert/alert2",
        json=alert_detail_response("alert2"),
        status=200,
        content_type="application/json",
    )


class PaloAltoPrismaCloudV1ApiTest(BaseTestCase):
    def setUp(self) -> None:
        super().setUp()

    @responses.activate
    def test_api_client_authentication(self):
        mock_response()
        api = ConnectorFactory.get_api(technology_id="palo_alto_prisma_cloud")

        client = api.api_client

        self.assertEqual(api.token, "mocked_token")
        self.assertIn("x-redlock-auth", client.headers)
        self.assertEqual(client.headers["x-redlock-auth"], "mocked_token")

    @responses.activate
    def test_list_alerts(self):
        mock_response()
        api = ConnectorFactory.get_api(technology_id="palo_alto_prisma_cloud")
        result = api.list_alerts()
        self.assertEqual(result, alert_response("alert1", "token1"))

    @responses.activate
    def test_get_alert_detail(self):
        mock_response()
        api = ConnectorFactory.get_api(technology_id="palo_alto_prisma_cloud")
        result = api.get_alert_detail("alert1")
        self.assertEqual(result, alert_detail_response("alert1"))

    @responses.activate
    def test_dismiss_alert(self):
        mock_response()
        api = ConnectorFactory.get_api(technology_id="palo_alto_prisma_cloud")
        result = api.dismiss_alert("alert1")
        self.assertEqual(result, {})

    @responses.activate
    def test_reopen_alert(self):
        mock_response()
        api = ConnectorFactory.get_api(technology_id="palo_alto_prisma_cloud")
        result = api.reopen_alert("alert1")
        self.assertEqual(result, {})


class PaloAltoPrismaCloudV1IntegrationTest(BaseIntegrationTest):
    def setUp(self) -> None:
        self.integration = ConnectorFactory.get_integration(
            technology_id="palo_alto_prisma_cloud",
            version_id="v1",
            enabled_actions=["event_sync", "update_lifecycle_status"],
        )

    def test_bookmarks(self):
        bookmark = self.integration.get_bookmark(IntegrationActionType.EVENT_SYNC)
        self.assertIsNotNone(bookmark)

        schema = PaloAltoPrismaCloudEventSyncBookmarks.model_json_schema()
        self.assertIn(IntegrationActionType.EVENT_SYNC.value, schema["properties"])

        schema = schema["properties"][IntegrationActionType.EVENT_SYNC.value]
        self.assertIn("query_receive_time", schema["properties"])

    @responses.activate
    def test_event_sync(self):
        mock_response()
        bookmark: PaloAltoPrismaCloudEventSyncBookmark = self.integration.get_bookmark(
            IntegrationActionType.EVENT_SYNC
        )
        self.assertIsNotNone(bookmark)
        bookmark.query_receive_time = (
            datetime.now(timezone.utc) - timedelta(minutes=100)
        ).isoformat()
        response = self.integration.invoke_action(
            IntegrationActionType.EVENT_SYNC,
            **{
                "bookmark": bookmark,
            },
        )
        result = serialize(list(response))
        self.assertEqual(
            result,
            [
                {
                    "raw_event": mock.ANY,
                    "vendor_group_ref": None,
                    "vendor_item_ref": {
                        "id": "alert1",
                        "title": "Palo Alto Prisma Cloud Alert",
                    },
                    "event_timestamp": "2026-10-01T00:00:00Z",
                    "ocsf": {
                        "activity_id": 99,
                        "activity_name": "Other",
                        "category_name": "Findings",
                        "category_uid": 2,
                        "class_name": "Detection Finding",
                        "class_uid": 2004,
                        "cloud": {
                            "account": {"name": "string", "uid": "string"},
                            "provider": "ALL",
                            "region": "string",
                        },
                        "comment": "string",
                        "confidence_score": 0,
                        "evidences": [
                            {
                                "data": {
                                    "bytes": 0,
                                    "bytes_in": 0,
                                    "bytes_out": 0,
                                    "feedSource": "string",
                                    "markedThreatTs": 0,
                                    "packets": 0,
                                    "timestamp": 0,
                                    "trafficOverTime": [{}],
                                },
                                "dst_endpoint": {"ip": "string", "isp": "string"},
                                "name": "string",
                                "src_endpoint": {"ip": "string", "isp": "string"},
                                "uid": "1234",
                                "verdict": "string",
                                "verdict_id": 99,
                            }
                        ],
                        "finding_info": {
                            "desc": "string",
                            "first_seen_time_dt": "2023-10-01T00:00:00Z",
                            "last_seen_time_dt": "2023-10-01T00:00:00Z",
                            "modified_time_dt": "2023-10-01T00:00:00Z",
                            "types": ["string"],
                            "uid": "string",
                        },
                        "impact": "string",
                        "impact_id": 99,
                        "impact_score": 12,
                        "message": "string",
                        "metadata": {
                            "correlation_uid": "alert1",
                            "event_code": None,
                            "profiles": [],
                            "uid": "alert1",
                            "version": "1.5.0-dev",
                        },
                        "resources": [
                            {
                                "created_time": 0,
                                "data": {
                                    "array": True,
                                    "bigDecimal": True,
                                    "bigInteger": True,
                                    "binary": True,
                                    "boolean": True,
                                    "containerNode": True,
                                    "double": True,
                                    "float": True,
                                    "floatingPointNumber": True,
                                    "int": True,
                                    "integralNumber": True,
                                    "long": True,
                                    "missingNode": True,
                                    "nodeType": "ARRAY",
                                    "null": True,
                                    "number": True,
                                    "object": True,
                                    "pojo": True,
                                    "short": True,
                                    "textual": True,
                                    "valueNode": True,
                                },
                                "modified_time": None,
                                "name": "string",
                                "owner": {"name": "string"},
                                "tags": [],
                                "type": "string",
                                "uid": "string",
                                "uid_alt": "string",
                            }
                        ],
                        "severity": "High",
                        "severity_id": 4,
                        "status": "New",
                        "status_id": 1,
                        "time": 1790812800000,
                        "time_dt": "2026-10-01T00:00:00Z",
                        "type_name": "Detection Finding: Other",
                        "type_uid": 200499,
                    },
                }
            ],
        )

    @responses.activate
    def test_update_lifecycle_status_dissmiss(self):
        mock_response()

        response = self.integration.invoke_action(
            IntegrationActionType.UPDATE_LIFECYCLE_STATUS,
            **{
                "vendor_sync_id": "12345",
                "status": CorrIncidentStatus.CLOSED,
            },
        )

        self.assertEqual(response, UpdateLifecycleStatusResult())

    @responses.activate
    def test_update_lifecycle_status_reopen(self):
        mock_response()

        response = self.integration.invoke_action(
            IntegrationActionType.UPDATE_LIFECYCLE_STATUS,
            **{
                "vendor_sync_id": "12345",
                "status": CorrIncidentStatus.ASSIGNED,
            },
        )

        self.assertEqual(response, UpdateLifecycleStatusResult())


def setup_basic_responses(fail=False):
    if fail:
        responses.add(
            responses.POST,
            f"https://test_base_url.com/login",
            json={},
            status=400,
            content_type="application/json",
        )
    else:
        responses.add(
            responses.POST,
            f"https://test_base_url.com/login",
            json={"token": "mocked_token"},
            status=200,
            content_type="application/json",
        )


class PaloAltoPrismaCloudV1HealthCheckTest(BaseTestCase):
    def setUp(self) -> None:
        self.integration = ConnectorFactory.get_integration(
            technology_id="palo_alto_prisma_cloud",
            version_id="v1",
        )

    @responses.activate
    def test_test_connection(self):
        setup_basic_responses()
        health_check = ConnectionHealthCheck(integration=self.integration)
        self.assertEqual(health_check.get_result(), IntegrationHealthCheckResult.PASSED)

    @responses.activate
    def test_test_connection_error(self):
        setup_basic_responses(fail=True)
        health_check = ConnectionHealthCheck(integration=self.integration)
        self.assertEqual(health_check.get_result(), IntegrationHealthCheckResult.FAILED)


class PaloAltoPrismaCloudV1HelpersTest(BaseTestCase):
    def test_parse_datetime(self):
        dt_str = "2023-10-01T00:00:00Z"
        parsed_dt = event_sync.parse_datetime(dt_str)
        self.assertEqual(parsed_dt.isoformat(), "2023-10-01T00:00:00+00:00")

    def test_format_datetime(self):
        dt = datetime(2023, 10, 1, 0, 0, tzinfo=timezone.utc)
        formatted_dt = event_sync.format_datetime(dt)
        self.assertEqual(formatted_dt, "2023-10-01T00:00:00Z")

    def test_get_iso_date(self):
        dt_str = "2023-10-01T00:00:00Z"
        iso_date = event_sync.get_iso_date(dt_str)
        self.assertEqual(iso_date, "2023-10-01T00:00:00+00:00")

    def test_get_severity(self):
        severity_map = {
            "critical": ocsf.Severity.CRITICAL,
            "high": ocsf.Severity.HIGH,
            "elevated": ocsf.Severity.OTHER,
            "medium": ocsf.Severity.MEDIUM,
            "low": ocsf.Severity.LOW,
            "unknown": ocsf.Severity.UNKNOWN,
        }
        for crlevel, expected_severity in severity_map.items():
            self.assertEqual(event_sync.get_severity(crlevel), expected_severity)

    def test_get_status(self):
        status_map = {
            "open": ocsf.DetectionStatus.NEW,
            "dismissed": ocsf.DetectionStatus.SUPPRESSED,
            "snoozed": ocsf.DetectionStatus.ARCHIVED,
            "resolved": ocsf.DetectionStatus.RESOLVED,
            "pending_resolution": ocsf.DetectionStatus.IN_PROGRESS,
        }
        for status, expected_status in status_map.items():
            self.assertEqual(event_sync.get_status(status), expected_status)
